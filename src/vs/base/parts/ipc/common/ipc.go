/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	basecommon "src/vs/base/common"
)

// IChannel is an abstraction over a collection of commands.
// You can call several commands on a channel, each taking at
// most one single argument. A call always returns a result
// with at most one single return value.
type IChannel interface {
	// Call invokes a command on the channel
	Call(command string, arg interface{}) (interface{}, error)

	// Listen subscribes to an event on the channel
	Listen(event string, arg interface{}) <-chan interface{}
}

// IServerChannel is the counter part to IChannel,
// on the server-side. You should implement this interface
// if you'd like to handle remote calls or events.
type IServerChannel[TContext any] interface {
	// Call handles a command call from a client
	Call(ctx TContext, command string, arg interface{}) (interface{}, error)

	// Listen handles an event subscription from a client
	Listen(ctx TContext, event string, arg interface{}) basecommon.Event[interface{}]
}

// RequestType represents the type of IPC request
type RequestType int

const (
	RequestTypePromise RequestType = iota + 100
	RequestTypePromiseCancel
	RequestTypeEventListen
	RequestTypeEventDispose
)

// ResponseType represents the type of IPC response
type ResponseType int

const (
	ResponseTypeInitialize ResponseType = iota + 200
	ResponseTypePromiseSuccess
	ResponseTypePromiseError
	ResponseTypePromiseErrorObj
	ResponseTypeEventFire
)

// IRawRequest represents a raw IPC request
type IRawRequest struct {
	Type        RequestType `json:"type"`
	ID          int         `json:"id"`
	ChannelName string      `json:"channelName,omitempty"`
	Name        string      `json:"name,omitempty"`
	Arg         interface{} `json:"arg,omitempty"`
}

// IRawResponse represents a raw IPC response
type IRawResponse struct {
	Type ResponseType `json:"type"`
	ID   int          `json:"id,omitempty"`
	Data interface{}  `json:"data,omitempty"`
}

// IMessagePassingProtocol represents a message passing protocol
type IMessagePassingProtocol interface {
	// Send sends a buffer over the protocol
	Send(buffer []byte)

	// OnMessage returns an event for incoming messages
	OnMessage() basecommon.Event[[]byte]
}

// ChannelClient provides a client-side implementation of IChannel
type ChannelClient struct {
	protocol    IMessagePassingProtocol
	channelName string
	requestID   int
	handlers    map[int]chan interface{}
}

// NewChannelClient creates a new channel client
func NewChannelClient(protocol IMessagePassingProtocol, channelName string) *ChannelClient {
	return &ChannelClient{
		protocol:    protocol,
		channelName: channelName,
		handlers:    make(map[int]chan interface{}),
	}
}

// Call implements IChannel.Call
func (c *ChannelClient) Call(command string, arg interface{}) (interface{}, error) {
	c.requestID++
	id := c.requestID

	// Create response channel
	responseChan := make(chan interface{}, 1)
	c.handlers[id] = responseChan

	// Send request
	request := &IRawRequest{
		Type:        RequestTypePromise,
		ID:          id,
		ChannelName: c.channelName,
		Name:        command,
		Arg:         arg,
	}

	// In a real implementation, this would serialize the request and send it
	// For now, we'll just return a placeholder
	return nil, nil
}

// Listen implements IChannel.Listen
func (c *ChannelClient) Listen(event string, arg interface{}) <-chan interface{} {
	c.requestID++
	id := c.requestID

	// Create event channel
	eventChan := make(chan interface{})

	// Send listen request
	request := &IRawRequest{
		Type:        RequestTypeEventListen,
		ID:          id,
		ChannelName: c.channelName,
		Name:        event,
		Arg:         arg,
	}

	// In a real implementation, this would serialize the request and send it
	// For now, we'll just return the channel
	_ = request
	return eventChan
}

// ChannelServer provides a server-side implementation for handling channels
type ChannelServer struct {
	protocol IMessagePassingProtocol
	channels map[string]IServerChannel[string]
}

// NewChannelServer creates a new channel server
func NewChannelServer(protocol IMessagePassingProtocol) *ChannelServer {
	return &ChannelServer{
		protocol: protocol,
		channels: make(map[string]IServerChannel[string]),
	}
}

// RegisterChannel registers a server channel
func (s *ChannelServer) RegisterChannel(channelName string, channel IServerChannel[string]) {
	s.channels[channelName] = channel
}

// HandleRequest handles an incoming request
func (s *ChannelServer) HandleRequest(request *IRawRequest) *IRawResponse {
	channel, exists := s.channels[request.ChannelName]
	if !exists {
		return &IRawResponse{
			Type: ResponseTypePromiseError,
			ID:   request.ID,
			Data: map[string]interface{}{
				"message": "Channel not found: " + request.ChannelName,
			},
		}
	}

	switch request.Type {
	case RequestTypePromise:
		result, err := channel.Call("", request.Name, request.Arg)
		if err != nil {
			return &IRawResponse{
				Type: ResponseTypePromiseError,
				ID:   request.ID,
				Data: map[string]interface{}{
					"message": err.Error(),
				},
			}
		}
		return &IRawResponse{
			Type: ResponseTypePromiseSuccess,
			ID:   request.ID,
			Data: result,
		}

	case RequestTypeEventListen:
		// In a real implementation, this would set up event forwarding
		return &IRawResponse{
			Type: ResponseTypePromiseSuccess,
			ID:   request.ID,
			Data: nil,
		}

	default:
		return &IRawResponse{
			Type: ResponseTypePromiseError,
			ID:   request.ID,
			Data: map[string]interface{}{
				"message": "Unknown request type",
			},
		}
	}
}

// SimpleChannel provides a simple implementation of IChannel for testing
type SimpleChannel struct {
	handlers map[string]func(interface{}) (interface{}, error)
	events   map[string]basecommon.Event[interface{}]
}

// NewSimpleChannel creates a new simple channel
func NewSimpleChannel() *SimpleChannel {
	return &SimpleChannel{
		handlers: make(map[string]func(interface{}) (interface{}, error)),
		events:   make(map[string]basecommon.Event[interface{}]),
	}
}

// Call implements IChannel.Call
func (c *SimpleChannel) Call(command string, arg interface{}) (interface{}, error) {
	if handler, exists := c.handlers[command]; exists {
		return handler(arg)
	}
	return nil, nil
}

// Listen implements IChannel.Listen
func (c *SimpleChannel) Listen(event string, arg interface{}) <-chan interface{} {
	if eventEmitter, exists := c.events[event]; exists {
		// Convert Event to channel
		ch := make(chan interface{})
		eventEmitter.Dispose() // Simplified - in real implementation would properly handle disposal
		return ch
	}
	return make(chan interface{})
}

// RegisterHandler registers a command handler
func (c *SimpleChannel) RegisterHandler(command string, handler func(interface{}) (interface{}, error)) {
	c.handlers[command] = handler
}

// RegisterEvent registers an event
func (c *SimpleChannel) RegisterEvent(event string, eventEmitter basecommon.Event[interface{}]) {
	c.events[event] = eventEmitter
}
