package common

// Set is a set of strings.
type Set[T comparable] struct {
	m map[T]struct{}
}

// NewSet creates a new set.
func NewSet[T comparable]() *Set[T] {
	return &Set[T]{
		m: make(map[T]struct{}),
	}
}

// Add adds a value to the set.
func (s *Set[T]) Add(value T) {
	s.m[value] = struct{}{}
}

// Has returns true if the set contains the value.
func (s *Set[T]) Has(value T) bool {
	_, ok := s.m[value]
	return ok
}

// ToSlice returns the set as a slice.
func (s *Set[T]) ToSlice() []T {
	slice := make([]T, 0, len(s.m))
	for k := range s.m {
		slice = append(slice, k)
	}
	return slice
}
