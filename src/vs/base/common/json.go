/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"encoding/json"
	"unicode"
)

// ScanError represents JSON scan errors
type ScanError int

const (
	ScanErrorNone ScanError = iota
	ScanErrorUnexpectedEndOfComment
	ScanErrorUnexpectedEndOfString
	ScanErrorUnexpectedEndOfNumber
	ScanErrorInvalidUnicode
	ScanErrorInvalidEscapeCharacter
	ScanErrorInvalidCharacter
)

// SyntaxKind represents JSON syntax kinds
type SyntaxKind int

const (
	SyntaxKindOpenBraceToken SyntaxKind = iota + 1
	SyntaxKindCloseBraceToken
	SyntaxKindOpenBracketToken
	SyntaxKindCloseBracketToken
	SyntaxKindCommaToken
	SyntaxKindColonToken
	SyntaxKindNullKeyword
	SyntaxKindTrueKeyword
	SyntaxKindFalseKeyword
	SyntaxKindStringLiteral
	SyntaxKindNumericLiteral
	SyntaxKindLineCommentTrivia
	SyntaxKindBlockCommentTrivia
	SyntaxKindLineBreakTrivia
	SyntaxKindTrivia
	SyntaxKindUnknown
	SyntaxKindEOF
)

// JSONScanner represents a JSON scanner at a position in the input string
type JSONScanner interface {
	// SetPosition sets the scan position to a new offset
	SetPosition(pos int)
	// Scan reads the next token and returns the token code
	Scan() SyntaxKind
	// GetPosition returns the current scan position
	GetPosition() int
	// GetToken returns the last read token
	GetToken() SyntaxKind
	// GetTokenValue returns the last read token value
	GetTokenValue() string
	// GetTokenOffset returns the start offset of the last read token
	GetTokenOffset() int
	// GetTokenLength returns the length of the last read token
	GetTokenLength() int
	// GetTokenError returns an error code of the last scan
	GetTokenError() ScanError
}

// ParseError represents a JSON parse error
type ParseError struct {
	Error  ParseErrorCode `json:"error"`
	Offset int            `json:"offset"`
	Length int            `json:"length"`
}

// ParseErrorCode represents JSON parse error codes
type ParseErrorCode int

const (
	ParseErrorCodeInvalidSymbol ParseErrorCode = iota + 1
	ParseErrorCodeInvalidNumberFormat
	ParseErrorCodePropertyNameExpected
	ParseErrorCodeValueExpected
	ParseErrorCodeColonExpected
	ParseErrorCodeCommaExpected
	ParseErrorCodeCloseBraceExpected
	ParseErrorCodeCloseBracketExpected
	ParseErrorCodeEndOfFileExpected
	ParseErrorCodeInvalidCommentToken
	ParseErrorCodeUnexpectedEndOfComment
	ParseErrorCodeUnexpectedEndOfString
	ParseErrorCodeUnexpectedEndOfNumber
	ParseErrorCodeInvalidUnicode
	ParseErrorCodeInvalidEscapeCharacter
	ParseErrorCodeInvalidCharacter
)

// Node represents a JSON AST node
type Node interface {
	GetType() string
	GetOffset() int
	GetLength() int
	GetParent() Node
	GetChildren() []Node
}

// Segment represents a path segment (string or number)
type Segment interface{}

// JSONPath represents a path in JSON (array of segments)
type JSONPath []Segment

// Location represents a location in JSON
type Location struct {
	// The previous property key or literal value
	PreviousNode interface{}
	// The path to the location
	Path JSONPath
	// If set, the location's offset is at a property key
	IsAtPropertyKey bool
}

// Matches checks if the location's path matches a pattern
func (l *Location) Matches(patterns JSONPath) bool {
	// Simplified pattern matching implementation
	if len(l.Path) != len(patterns) {
		return false
	}

	for i, pattern := range patterns {
		if pattern == "*" {
			continue // wildcard matches anything
		}
		if pattern != l.Path[i] {
			return false
		}
	}

	return true
}

// ParseOptions represents options for JSON parsing
type ParseOptions struct {
	AllowTrailingComma bool
	AllowEmptyContent  bool
	DisallowComments   bool
}

// Default parse options
var ParseOptionsDefault = ParseOptions{
	AllowTrailingComma: false,
	AllowEmptyContent:  false,
	DisallowComments:   false,
}

// JSONVisitor represents a visitor for JSON parsing
type JSONVisitor struct {
	OnObjectBegin    func(offset, length int)
	OnObjectProperty func(property string, offset, length int)
	OnObjectEnd      func(offset, length int)
	OnArrayBegin     func(offset, length int)
	OnArrayEnd       func(offset, length int)
	OnLiteralValue   func(value interface{}, offset, length int)
	OnSeparator      func(character string, offset, length int)
	OnComment        func(offset, length int)
	OnError          func(error ParseErrorCode, offset, length int)
}

// CreateScanner creates a new JSON scanner
func CreateScanner(text string, ignoreTrivia bool) JSONScanner {
	return &jsonScanner{
		text:         text,
		pos:          0,
		len:          len(text),
		ignoreTrivia: ignoreTrivia,
		token:        SyntaxKindUnknown,
		tokenOffset:  0,
		tokenLength:  0,
		tokenValue:   "",
		tokenError:   ScanErrorNone,
	}
}

// jsonScanner implements JSONScanner
type jsonScanner struct {
	text         string
	pos          int
	len          int
	ignoreTrivia bool
	token        SyntaxKind
	tokenOffset  int
	tokenLength  int
	tokenValue   string
	tokenError   ScanError
}

func (s *jsonScanner) SetPosition(pos int) {
	s.pos = pos
	s.token = SyntaxKindUnknown
	s.tokenOffset = 0
	s.tokenLength = 0
	s.tokenValue = ""
	s.tokenError = ScanErrorNone
}

func (s *jsonScanner) Scan() SyntaxKind {
	s.tokenOffset = s.pos
	s.tokenError = ScanErrorNone
	s.tokenValue = ""

	if s.pos >= s.len {
		s.tokenLength = 0
		s.token = SyntaxKindEOF
		return s.token
	}

	ch := s.text[s.pos]
	s.pos++

	// Skip whitespace
	for unicode.IsSpace(rune(ch)) && s.pos < s.len {
		ch = s.text[s.pos]
		s.pos++
	}

	if s.pos > s.len {
		s.tokenLength = s.pos - s.tokenOffset
		s.token = SyntaxKindEOF
		return s.token
	}

	switch ch {
	case '{':
		s.token = SyntaxKindOpenBraceToken
	case '}':
		s.token = SyntaxKindCloseBraceToken
	case '[':
		s.token = SyntaxKindOpenBracketToken
	case ']':
		s.token = SyntaxKindCloseBracketToken
	case ',':
		s.token = SyntaxKindCommaToken
	case ':':
		s.token = SyntaxKindColonToken
	case '"':
		s.token = SyntaxKindStringLiteral
		s.scanString()
	case '/':
		if s.pos < s.len && s.text[s.pos] == '/' {
			s.token = SyntaxKindLineCommentTrivia
			s.scanLineComment()
		} else if s.pos < s.len && s.text[s.pos] == '*' {
			s.token = SyntaxKindBlockCommentTrivia
			s.scanBlockComment()
		} else {
			s.token = SyntaxKindUnknown
		}
	default:
		if unicode.IsDigit(rune(ch)) || ch == '-' {
			s.token = SyntaxKindNumericLiteral
			s.pos-- // Back up to re-read the digit
			s.scanNumber()
		} else if unicode.IsLetter(rune(ch)) {
			s.pos-- // Back up to re-read the letter
			s.scanKeyword()
		} else {
			s.token = SyntaxKindUnknown
		}
	}

	s.tokenLength = s.pos - s.tokenOffset
	return s.token
}

func (s *jsonScanner) scanString() {
	start := s.pos
	for s.pos < s.len {
		ch := s.text[s.pos]
		if ch == '"' {
			s.tokenValue = s.text[start:s.pos]
			s.pos++
			return
		}
		if ch == '\\' {
			s.pos++
			if s.pos >= s.len {
				s.tokenError = ScanErrorUnexpectedEndOfString
				return
			}
		}
		s.pos++
	}
	s.tokenError = ScanErrorUnexpectedEndOfString
}

func (s *jsonScanner) scanNumber() {
	start := s.pos
	if s.pos < s.len && s.text[s.pos] == '-' {
		s.pos++
	}

	for s.pos < s.len && unicode.IsDigit(rune(s.text[s.pos])) {
		s.pos++
	}

	if s.pos < s.len && s.text[s.pos] == '.' {
		s.pos++
		for s.pos < s.len && unicode.IsDigit(rune(s.text[s.pos])) {
			s.pos++
		}
	}

	if s.pos < s.len && (s.text[s.pos] == 'e' || s.text[s.pos] == 'E') {
		s.pos++
		if s.pos < s.len && (s.text[s.pos] == '+' || s.text[s.pos] == '-') {
			s.pos++
		}
		for s.pos < s.len && unicode.IsDigit(rune(s.text[s.pos])) {
			s.pos++
		}
	}

	s.tokenValue = s.text[start:s.pos]
}

func (s *jsonScanner) scanKeyword() {
	start := s.pos
	for s.pos < s.len && unicode.IsLetter(rune(s.text[s.pos])) {
		s.pos++
	}

	value := s.text[start:s.pos]
	s.tokenValue = value

	switch value {
	case "null":
		s.token = SyntaxKindNullKeyword
	case "true":
		s.token = SyntaxKindTrueKeyword
	case "false":
		s.token = SyntaxKindFalseKeyword
	default:
		s.token = SyntaxKindUnknown
	}
}

func (s *jsonScanner) scanLineComment() {
	start := s.pos
	for s.pos < s.len && s.text[s.pos] != '\n' && s.text[s.pos] != '\r' {
		s.pos++
	}
	s.tokenValue = s.text[start:s.pos]
}

func (s *jsonScanner) scanBlockComment() {
	start := s.pos
	s.pos++ // skip '*'
	for s.pos < s.len-1 {
		if s.text[s.pos] == '*' && s.text[s.pos+1] == '/' {
			s.pos += 2
			s.tokenValue = s.text[start : s.pos-2]
			return
		}
		s.pos++
	}
	s.tokenError = ScanErrorUnexpectedEndOfComment
}

func (s *jsonScanner) GetPosition() int         { return s.pos }
func (s *jsonScanner) GetToken() SyntaxKind     { return s.token }
func (s *jsonScanner) GetTokenValue() string    { return s.tokenValue }
func (s *jsonScanner) GetTokenOffset() int      { return s.tokenOffset }
func (s *jsonScanner) GetTokenLength() int      { return s.tokenLength }
func (s *jsonScanner) GetTokenError() ScanError { return s.tokenError }

// ParseJSON parses JSON text and returns the parsed object
func ParseJSON(text string, errors *[]ParseError, options *ParseOptions) interface{} {
	if options == nil {
		options = &ParseOptionsDefault
	}

	// Use Go's standard JSON parser for simplicity
	// In a full implementation, you'd implement a custom parser
	var result interface{}
	err := json.Unmarshal([]byte(text), &result)
	if err != nil && errors != nil {
		*errors = append(*errors, ParseError{
			Error:  ParseErrorCodeInvalidSymbol,
			Offset: 0,
			Length: len(text),
		})
	}

	return result
}
