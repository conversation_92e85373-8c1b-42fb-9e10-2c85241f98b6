/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"crypto/rand"
	"fmt"
	"regexp"
)

var uuidPattern = regexp.MustCompile(`^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$`)

// IsUUID checks if the given string is a valid UUID
func IsUUID(value string) bool {
	return uuidPattern.MatchString(value)
}

// GenerateUuid generates a new UUID v4
func GenerateUuid() string {
	// Generate 16 random bytes
	data := make([]byte, 16)
	_, err := rand.Read(data)
	if err != nil {
		panic(err)
	}

	// Set version bits (version 4)
	data[6] = (data[6] & 0x0f) | 0x40

	// Set variant bits
	data[8] = (data[8] & 0x3f) | 0x80

	// Format as UUID string
	return fmt.Sprintf("%08x-%04x-%04x-%04x-%012x",
		data[0:4],
		data[4:6],
		data[6:8],
		data[8:10],
		data[10:16])
}
