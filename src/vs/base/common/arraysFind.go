/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

// Comparator is a function that compares two values of type T.
type Comparator[T any] func(a, b T) int

// FindLast returns the last element in the array that matches the predicate.
func FindLast[T any](array []T, predicate func(T) bool, fromIndex ...int) *T {
	idx := FindLastIdx(array, predicate, fromIndex...)
	if idx == -1 {
		return nil
	}
	return &array[idx]
}

// FindLastIdx returns the index of the last element in the array that matches the predicate.
func FindLastIdx[T any](array []T, predicate func(T) bool, fromIndex ...int) int {
	start := len(array) - 1
	if len(fromIndex) > 0 {
		start = fromIndex[0]
	}
	for i := start; i >= 0; i-- {
		if predicate(array[i]) {
			return i
		}
	}
	return -1
}

// FindLastMonotonous finds the last item where predicate is true using binary search.
// Predicate must be monotonous: [true, ..., true, false, ..., false].
func FindLastMonotonous[T any](array []T, predicate func(T) bool) *T {
	idx := FindLastIdxMonotonous(array, predicate, 0, len(array))
	if idx == -1 {
		return nil
	}
	return &array[idx]
}

// FindLastIdxMonotonous finds the last index where predicate is true using binary search.
func FindLastIdxMonotonous[T any](array []T, predicate func(T) bool, startIdx, endIdxEx int) int {
	i := startIdx
	j := endIdxEx
	for i < j {
		k := (i + j) / 2
		if predicate(array[k]) {
			i = k + 1
		} else {
			j = k
		}
	}
	return i - 1
}

// FindFirstMonotonous finds the first item where predicate is true using binary search.
func FindFirstMonotonous[T any](array []T, predicate func(T) bool) *T {
	idx := FindFirstIdxMonotonousOrArrLen(array, predicate, 0, len(array))
	if idx == len(array) {
		return nil
	}
	return &array[idx]
}

// FindFirstIdxMonotonousOrArrLen finds the first index where predicate is true using binary search.
func FindFirstIdxMonotonousOrArrLen[T any](array []T, predicate func(T) bool, startIdx, endIdxEx int) int {
	i := startIdx
	j := endIdxEx
	for i < j {
		k := (i + j) / 2
		if predicate(array[k]) {
			j = k
		} else {
			i = k + 1
		}
	}
	return i
}

// FindFirstIdxMonotonous finds the first index where predicate is true using binary search.
func FindFirstIdxMonotonous[T any](array []T, predicate func(T) bool, startIdx, endIdxEx int) int {
	idx := FindFirstIdxMonotonousOrArrLen(array, predicate, startIdx, endIdxEx)
	if idx == len(array) {
		return -1
	}
	return idx
}

// MonotonousArray is a helper for repeated monotonous binary searches.
type MonotonousArray[T any] struct {
	array                     []T
	findLastMonotonousLastIdx int
	prevFindLastPredicate     func(T) bool
}

// NewMonotonousArray creates a new MonotonousArray.
func NewMonotonousArray[T any](array []T) *MonotonousArray[T] {
	return &MonotonousArray[T]{array: array}
}

// FindLastMonotonous finds the last item where predicate is true, assuming predicate is weaker or equal to previous.
func (m *MonotonousArray[T]) FindLastMonotonous(predicate func(T) bool) *T {
	idx := FindLastIdxMonotonous(m.array, predicate, m.findLastMonotonousLastIdx, len(m.array))
	m.findLastMonotonousLastIdx = idx + 1
	return func() *T {
		if idx == -1 {
			return nil
		}
		return &m.array[idx]
	}()
}

// FindFirstMax returns the first item that is equal to or greater than every other item.
func FindFirstMax[T any](array []T, comparator Comparator[T]) *T {
	if len(array) == 0 {
		return nil
	}
	max := array[0]
	for i := 1; i < len(array); i++ {
		if comparator(array[i], max) > 0 {
			max = array[i]
		}
	}
	return &max
}

// FindLastMax returns the last item that is equal to or greater than every other item.
func FindLastMax[T any](array []T, comparator Comparator[T]) *T {
	if len(array) == 0 {
		return nil
	}
	max := array[0]
	for i := 1; i < len(array); i++ {
		if comparator(array[i], max) >= 0 {
			max = array[i]
		}
	}
	return &max
}

// FindFirstMin returns the first item that is equal to or less than every other item.
func FindFirstMin[T any](array []T, comparator Comparator[T]) *T {
	return FindFirstMax(array, func(a, b T) int { return -comparator(a, b) })
}

// FindMaxIdx returns the index of the maximum element in the array.
func FindMaxIdx[T any](array []T, comparator Comparator[T]) int {
	if len(array) == 0 {
		return -1
	}
	maxIdx := 0
	for i := 1; i < len(array); i++ {
		if comparator(array[i], array[maxIdx]) > 0 {
			maxIdx = i
		}
	}
	return maxIdx
}

// MapFindFirst returns the first mapped value of the array which is not nil.
func MapFindFirst[T any, R any](items []T, mapFn func(T) *R) *R {
	for _, value := range items {
		mapped := mapFn(value)
		if mapped != nil {
			return mapped
		}
	}
	return nil
}
