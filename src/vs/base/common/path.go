/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

// NOTE: VS Code's copy of nodejs path library to be usable in common (non-node) namespace
// Translated from TypeScript to Go

package common

import (
	"fmt"
	"path/filepath"
	"strings"
)

// Character constants
const (
	CharUppercaseA    = 65  // A
	CharLowercaseA    = 97  // a
	CharUppercaseZ    = 90  // Z
	CharLowercaseZ    = 122 // z
	CharDot           = 46  // .
	CharForwardSlash  = 47  // /
	CharBackwardSlash = 92  // \
	CharColon         = 58  // :
	CharQuestionMark  = 63  // ?
)

// ErrorInvalidArgType represents an invalid argument type error
type ErrorInvalidArgType struct {
	Name     string
	Expected string
	Actual   interface{}
	Code     string
}

func (e *ErrorInvalidArgType) Error() string {
	determiner := "must be"
	expected := e.Expected
	if strings.HasPrefix(expected, "not ") {
		determiner = "must not be"
		expected = strings.TrimPrefix(expected, "not ")
	}

	argType := "argument"
	if strings.Contains(e.Name, ".") {
		argType = "property"
	}

	return fmt.Sprintf(`The "%s" %s %s of type %s. Received type %T`,
		e.Name, argType, determiner, expected, e.Actual)
}

// validateString validates that a value is a string
func validateString(value interface{}, name string) error {
	if _, ok := value.(string); !ok {
		return &ErrorInvalidArgType{
			Name:     name,
			Expected: "string",
			Actual:   value,
			Code:     "ERR_INVALID_ARG_TYPE",
		}
	}
	return nil
}

// validateObject validates that a value is an object
func validateObject(value interface{}, name string) error {
	if value == nil {
		return &ErrorInvalidArgType{
			Name:     name,
			Expected: "Object",
			Actual:   value,
			Code:     "ERR_INVALID_ARG_TYPE",
		}
	}
	return nil
}

// ParsedPath represents a parsed path object
type ParsedPath struct {
	Root string `json:"root"`
	Dir  string `json:"dir"`
	Base string `json:"base"`
	Ext  string `json:"ext"`
	Name string `json:"name"`
}

// IPath interface defines path manipulation methods
type IPath interface {
	Normalize(path string) string
	IsAbsolute(path string) bool
	Join(paths ...string) string
	Resolve(pathSegments ...string) string
	Relative(from, to string) string
	Dirname(path string) string
	Basename(path string, suffix ...string) string
	Extname(path string) string
	Format(pathObject ParsedPath) string
	Parse(path string) ParsedPath
	ToNamespacedPath(path string) string
	Sep() string
	Delimiter() string
}

// Global platform detection
var platformIsWin32 = GetPlatform() == PlatformWindows

// isPathSeparator checks if a character code is a path separator
func isPathSeparator(code byte) bool {
	return code == CharForwardSlash || code == CharBackwardSlash
}

// isPosixPathSeparator checks if a character code is a POSIX path separator
func isPosixPathSeparator(code byte) bool {
	return code == CharForwardSlash
}

// isWindowsDeviceRoot checks if a character code is a Windows device root
func isWindowsDeviceRoot(code byte) bool {
	return (code >= CharUppercaseA && code <= CharUppercaseZ) ||
		(code >= CharLowercaseA && code <= CharLowercaseZ)
}

// normalizeString resolves . and .. elements in a path with directory names
func normalizeString(path string, allowAboveRoot bool, separator string, isPathSeparatorFunc func(byte) bool) string {
	res := ""
	lastSegmentLength := 0
	lastSlash := -1
	dots := 0
	var code byte

	for i := 0; i <= len(path); i++ {
		if i < len(path) {
			code = path[i]
		} else if isPathSeparatorFunc(code) {
			break
		} else {
			code = CharForwardSlash
		}

		if isPathSeparatorFunc(code) {
			if lastSlash == i-1 || dots == 1 {
				// NOOP
			} else if dots == 2 {
				if len(res) < 2 || lastSegmentLength != 2 ||
					(len(res) > 0 && res[len(res)-1] != '.') ||
					(len(res) > 1 && res[len(res)-2] != '.') {
					if len(res) > 2 {
						lastSlashIndex := strings.LastIndex(res, separator)
						if lastSlashIndex == -1 {
							res = ""
							lastSegmentLength = 0
						} else {
							res = res[:lastSlashIndex]
							lastSegmentLength = len(res) - 1 - strings.LastIndex(res, separator)
						}
						lastSlash = i
						dots = 0
						continue
					} else if len(res) != 0 {
						res = ""
						lastSegmentLength = 0
						lastSlash = i
						dots = 0
						continue
					}
				}
				if allowAboveRoot {
					if len(res) > 0 {
						res += separator + ".."
					} else {
						res = ".."
					}
					lastSegmentLength = 2
				}
			} else {
				if len(res) > 0 {
					res += separator + path[lastSlash+1:i]
				} else {
					res = path[lastSlash+1 : i]
				}
				lastSegmentLength = i - lastSlash - 1
			}
			lastSlash = i
			dots = 0
		} else if code == CharDot && dots != -1 {
			dots++
		} else {
			dots = -1
		}
	}
	return res
}

// formatExt formats an extension string
func formatExt(ext string) string {
	if ext == "" {
		return ""
	}
	if ext[0] == '.' {
		return ext
	}
	return "." + ext
}

// format formats a path object into a path string
func format(sep string, pathObject ParsedPath) string {
	dir := pathObject.Dir
	if dir == "" {
		dir = pathObject.Root
	}

	base := pathObject.Base
	if base == "" {
		base = pathObject.Name + formatExt(pathObject.Ext)
	}

	if dir == "" {
		return base
	}

	if dir == pathObject.Root {
		return dir + base
	}

	return dir + sep + base
}

// Win32Path implements IPath for Windows
type Win32Path struct{}

// Normalize normalizes a Windows path
func (w *Win32Path) Normalize(path string) string {
	if err := validateString(path, "path"); err != nil {
		panic(err)
	}

	length := len(path)
	if length == 0 {
		return "."
	}

	// Use Go's built-in filepath.Clean for basic normalization
	// This is a simplified version - the full implementation would
	// handle all the Windows-specific cases from the TypeScript version
	cleaned := filepath.Clean(path)

	// Convert forward slashes to backslashes on Windows
	if platformIsWin32 {
		cleaned = strings.ReplaceAll(cleaned, "/", "\\")
	}

	return cleaned
}

// IsAbsolute checks if a path is absolute on Windows
func (w *Win32Path) IsAbsolute(path string) bool {
	if err := validateString(path, "path"); err != nil {
		panic(err)
	}

	length := len(path)
	if length == 0 {
		return false
	}

	code := path[0]
	return isPathSeparator(code) ||
		// Possible device root
		(length > 2 &&
			isWindowsDeviceRoot(code) &&
			path[1] == ':' &&
			isPathSeparator(path[2]))
}

// Join joins path segments for Windows
func (w *Win32Path) Join(paths ...string) string {
	if len(paths) == 0 {
		return "."
	}

	var joined string
	for i, arg := range paths {
		if err := validateString(arg, "path"); err != nil {
			panic(err)
		}
		if len(arg) > 0 {
			if joined == "" {
				joined = arg
			} else {
				joined += "\\" + arg
			}
		}
		_ = i // Suppress unused variable warning
	}

	if joined == "" {
		return "."
	}

	return w.Normalize(joined)
}

// Resolve resolves path segments to an absolute path for Windows
func (w *Win32Path) Resolve(pathSegments ...string) string {
	// Simplified implementation using Go's filepath.Abs
	// The full implementation would handle all Windows-specific resolution logic
	var resolvedPath string

	for i := len(pathSegments) - 1; i >= 0; i-- {
		segment := pathSegments[i]
		if err := validateString(segment, fmt.Sprintf("paths[%d]", i)); err != nil {
			panic(err)
		}

		if len(segment) == 0 {
			continue
		}

		if resolvedPath == "" {
			resolvedPath = segment
		} else {
			resolvedPath = segment + "\\" + resolvedPath
		}

		if w.IsAbsolute(resolvedPath) {
			break
		}
	}

	if !w.IsAbsolute(resolvedPath) {
		if cwd := Cwd(); cwd != "" {
			resolvedPath = cwd + "\\" + resolvedPath
		}
	}

	return w.Normalize(resolvedPath)
}

// Relative calculates the relative path from 'from' to 'to' on Windows
func (w *Win32Path) Relative(from, to string) string {
	if err := validateString(from, "from"); err != nil {
		panic(err)
	}
	if err := validateString(to, "to"); err != nil {
		panic(err)
	}

	if from == to {
		return ""
	}

	// Simplified implementation using Go's filepath.Rel
	rel, err := filepath.Rel(from, to)
	if err != nil {
		return to
	}

	// Convert to Windows path separators
	if platformIsWin32 {
		rel = strings.ReplaceAll(rel, "/", "\\")
	}

	return rel
}

// Dirname returns the directory name of a path on Windows
func (w *Win32Path) Dirname(path string) string {
	if err := validateString(path, "path"); err != nil {
		panic(err)
	}

	if len(path) == 0 {
		return "."
	}

	dir := filepath.Dir(path)
	if platformIsWin32 {
		dir = strings.ReplaceAll(dir, "/", "\\")
	}

	return dir
}

// Basename returns the last portion of a path on Windows
func (w *Win32Path) Basename(path string, suffix ...string) string {
	if err := validateString(path, "path"); err != nil {
		panic(err)
	}

	base := filepath.Base(path)

	if len(suffix) > 0 && suffix[0] != "" {
		if err := validateString(suffix[0], "suffix"); err != nil {
			panic(err)
		}
		if strings.HasSuffix(base, suffix[0]) {
			base = base[:len(base)-len(suffix[0])]
		}
	}

	return base
}

// Extname returns the extension of a path on Windows
func (w *Win32Path) Extname(path string) string {
	if err := validateString(path, "path"); err != nil {
		panic(err)
	}

	return filepath.Ext(path)
}

// Format formats a path object into a Windows path string
func (w *Win32Path) Format(pathObject ParsedPath) string {
	if err := validateObject(pathObject, "pathObject"); err != nil {
		panic(err)
	}

	return format("\\", pathObject)
}

// Parse parses a Windows path into its components
func (w *Win32Path) Parse(path string) ParsedPath {
	if err := validateString(path, "path"); err != nil {
		panic(err)
	}

	ret := ParsedPath{Root: "", Dir: "", Base: "", Ext: "", Name: ""}
	if len(path) == 0 {
		return ret
	}

	// Simplified parsing using Go's filepath functions
	ret.Dir = filepath.Dir(path)
	ret.Base = filepath.Base(path)
	ret.Ext = filepath.Ext(path)
	ret.Name = strings.TrimSuffix(ret.Base, ret.Ext)

	// Determine root
	if w.IsAbsolute(path) {
		if len(path) >= 2 && path[1] == ':' {
			ret.Root = path[:2] + "\\"
		} else if strings.HasPrefix(path, "\\\\") {
			// UNC path
			parts := strings.Split(path, "\\")
			if len(parts) >= 4 {
				ret.Root = "\\\\" + parts[2] + "\\" + parts[3] + "\\"
			}
		} else {
			ret.Root = "\\"
		}
	}

	return ret
}

// ToNamespacedPath converts a path to a namespaced path on Windows
func (w *Win32Path) ToNamespacedPath(path string) string {
	if len(path) == 0 {
		return path
	}

	resolvedPath := w.Resolve(path)

	if len(resolvedPath) <= 2 {
		return path
	}

	if resolvedPath[0] == '\\' {
		// Possible UNC root
		if resolvedPath[1] == '\\' {
			code := resolvedPath[2]
			if code != '?' && code != '.' {
				// Matched non-long UNC root, convert the path to a long UNC path
				return "\\\\?\\UNC\\" + resolvedPath[2:]
			}
		}
	} else if isWindowsDeviceRoot(resolvedPath[0]) &&
		resolvedPath[1] == ':' &&
		resolvedPath[2] == '\\' {
		// Matched device root, convert the path to a long UNC path
		return "\\\\?\\" + resolvedPath
	}

	return resolvedPath
}

// Sep returns the path separator for Windows
func (w *Win32Path) Sep() string {
	return "\\"
}

// Delimiter returns the path delimiter for Windows
func (w *Win32Path) Delimiter() string {
	return ";"
}

// PosixPath implements IPath for POSIX systems
type PosixPath struct{}

func (p *PosixPath) Normalize(path string) string {
	if err := validateString(path, "path"); err != nil {
		panic(err)
	}
	if len(path) == 0 {
		return "."
	}
	return filepath.Clean(path)
}

func (p *PosixPath) IsAbsolute(path string) bool {
	return len(path) > 0 && path[0] == '/'
}

func (p *PosixPath) Join(paths ...string) string {
	return filepath.Join(paths...)
}

func (p *PosixPath) Resolve(pathSegments ...string) string {
	if len(pathSegments) == 0 {
		return ""
	}

	var resolvedPath string
	for _, segment := range pathSegments {
		if len(segment) > 0 {
			resolvedPath = segment
			break
		}
	}

	if resolvedPath == "" {
		return ""
	}

	if !p.IsAbsolute(resolvedPath) {
		if cwd := Cwd(); cwd != "" {
			resolvedPath = filepath.Join(cwd, resolvedPath)
		}
	}

	return p.Normalize(resolvedPath)
}

func (p *PosixPath) Relative(from, to string) string {
	rel, err := filepath.Rel(from, to)
	if err != nil {
		return to
	}
	return rel
}

func (p *PosixPath) Dirname(path string) string {
	return filepath.Dir(path)
}

func (p *PosixPath) Basename(path string, suffix ...string) string {
	base := filepath.Base(path)
	if len(suffix) > 0 && suffix[0] != "" {
		if strings.HasSuffix(base, suffix[0]) {
			base = base[:len(base)-len(suffix[0])]
		}
	}
	return base
}

func (p *PosixPath) Extname(path string) string {
	return filepath.Ext(path)
}

func (p *PosixPath) Format(pathObject ParsedPath) string {
	return format("/", pathObject)
}

func (p *PosixPath) Parse(path string) ParsedPath {
	ret := ParsedPath{}
	if len(path) == 0 {
		return ret
	}

	ret.Dir = filepath.Dir(path)
	ret.Base = filepath.Base(path)
	ret.Ext = filepath.Ext(path)
	ret.Name = strings.TrimSuffix(ret.Base, ret.Ext)

	if p.IsAbsolute(path) {
		ret.Root = "/"
	}

	return ret
}

func (p *PosixPath) ToNamespacedPath(path string) string {
	return path // No namespaced paths on POSIX
}

func (p *PosixPath) Sep() string {
	return "/"
}

func (p *PosixPath) Delimiter() string {
	return ":"
}

// Global path instances
var (
	Win32 = &Win32Path{}
	Posix = &PosixPath{}
)

// Default path functions based on platform
var (
	Normalize        func(string) string
	IsAbsolute       func(string) bool
	Join             func(...string) string
	Resolve          func(...string) string
	Relative         func(string, string) string
	Dirname          func(string) string
	Basename         func(string, ...string) string
	Extname          func(string) string
	Format           func(ParsedPath) string
	Parse            func(string) ParsedPath
	ToNamespacedPath func(string) string
	Sep              string
	Delimiter        string
)

func init() {
	if platformIsWin32 {
		Normalize = Win32.Normalize
		IsAbsolute = Win32.IsAbsolute
		Join = Win32.Join
		Resolve = Win32.Resolve
		Relative = Win32.Relative
		Dirname = Win32.Dirname
		Basename = Win32.Basename
		Extname = Win32.Extname
		Format = Win32.Format
		Parse = Win32.Parse
		ToNamespacedPath = Win32.ToNamespacedPath
		Sep = Win32.Sep()
		Delimiter = Win32.Delimiter()
	} else {
		Normalize = Posix.Normalize
		IsAbsolute = Posix.IsAbsolute
		Join = Posix.Join
		Resolve = Posix.Resolve
		Relative = Posix.Relative
		Dirname = Posix.Dirname
		Basename = Posix.Basename
		Extname = Posix.Extname
		Format = Posix.Format
		Parse = Posix.Parse
		ToNamespacedPath = Posix.ToNamespacedPath
		Sep = Posix.Sep()
		Delimiter = Posix.Delimiter()
	}
}

// Exported path separator constant
const SepChar = '/'
