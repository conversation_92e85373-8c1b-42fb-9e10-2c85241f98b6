package common

import (
	"os"
	"runtime"
)

// IsWindows returns true if the current platform is Windows.
var IsWindows = runtime.GOOS == "windows"

// IsMacintosh returns true if the current platform is macOS.
var IsMacintosh = runtime.GOOS == "darwin"

// IsLinux returns true if the current platform is Linux.
var IsLinux = runtime.GOOS == "linux"

// Platform is an enumeration of the different platforms.
type Platform int

const (
	// PlatformWindows represents the Windows platform.
	PlatformWindows Platform = iota
	// PlatformMac represents the macOS platform.
	PlatformMac
	// PlatformLinux represents the Linux platform.
	PlatformLinux
)

// GetPlatform returns the current platform.
func GetPlatform() Platform {
	switch runtime.GOOS {
	case "windows":
		return PlatformWindows
	case "darwin":
		return PlatformMac
	default:
		return PlatformLinux
	}
}

// Cwd returns the current working directory.
func Cwd() (string, error) {
	return os.Getwd()
}
