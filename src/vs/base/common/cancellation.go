/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"sync"
	"time"
)

// CancellationToken represents a token that can be cancelled
type CancellationToken interface {
	// IsCancellationRequested returns true if cancellation has been requested
	IsCancellationRequested() bool

	// OnCancellationRequested returns an event that fires when cancellation is requested
	OnCancellationRequested() Event[interface{}]
}

// cancellationTokenNone is a token that is never cancelled
type cancellationTokenNone struct{}

func (c *cancellationTokenNone) IsCancellationRequested() bool {
	return false
}

func (c *cancellationTokenNone) OnCancellationRequested() Event[interface{}] {
	return EventNone[interface{}]()
}

// cancellationTokenCancelled is a token that is already cancelled
type cancellationTokenCancelled struct{}

func (c *cancellationTokenCancelled) IsCancellationRequested() bool {
	return true
}

func (c *cancellationTokenCancelled) OnCancellationRequested() Event[interface{}] {
	// Create an event that fires immediately
	emitter := NewEmitter[interface{}]()
	go func() {
		emitter.Fire(nil)
	}()
	return emitter
}

// Global cancellation token constants
var (
	// CancellationTokenNone is a token that will never be cancelled
	CancellationTokenNone CancellationToken = &cancellationTokenNone{}

	// CancellationTokenCancelled is a token that is already cancelled
	CancellationTokenCancelled CancellationToken = &cancellationTokenCancelled{}
)

// IsCancellationToken checks if an object is a cancellation token
func IsCancellationToken(thing interface{}) bool {
	if thing == CancellationTokenNone || thing == CancellationTokenCancelled {
		return true
	}

	_, ok := thing.(CancellationToken)
	return ok
}

// MutableToken is a cancellation token that can be cancelled
type MutableToken struct {
	isCancelled bool
	emitter     *Emitter[interface{}]
	mu          sync.RWMutex
}

// NewMutableToken creates a new mutable cancellation token
func NewMutableToken() *MutableToken {
	return &MutableToken{}
}

// Cancel cancels the token
func (mt *MutableToken) Cancel() {
	mt.mu.Lock()
	defer mt.mu.Unlock()

	if !mt.isCancelled {
		mt.isCancelled = true
		if mt.emitter != nil {
			mt.emitter.Fire(nil)
			mt.emitter.Dispose()
			mt.emitter = nil
		}
	}
}

// IsCancellationRequested returns true if the token has been cancelled
func (mt *MutableToken) IsCancellationRequested() bool {
	mt.mu.RLock()
	defer mt.mu.RUnlock()
	return mt.isCancelled
}

// OnCancellationRequested returns an event that fires when cancellation is requested
func (mt *MutableToken) OnCancellationRequested() Event[interface{}] {
	mt.mu.Lock()
	defer mt.mu.Unlock()

	if mt.isCancelled {
		// Return an event that fires immediately
		emitter := NewEmitter[interface{}]()
		go func() {
			emitter.Fire(nil)
		}()
		return emitter
	}

	if mt.emitter == nil {
		mt.emitter = NewEmitter[interface{}]()
	}

	return mt.emitter
}

// Dispose disposes the token
func (mt *MutableToken) Dispose() {
	mt.mu.Lock()
	defer mt.mu.Unlock()

	if mt.emitter != nil {
		mt.emitter.Dispose()
		mt.emitter = nil
	}
}

// CancellationTokenSource manages a cancellation token that can be cancelled
type CancellationTokenSource struct {
	token          CancellationToken
	parentListener IDisposable
	mu             sync.Mutex
}

// NewCancellationTokenSource creates a new cancellation token source
func NewCancellationTokenSource(parent CancellationToken) *CancellationTokenSource {
	cts := &CancellationTokenSource{}

	if parent != nil {
		cts.parentListener = parent.OnCancellationRequested().Subscribe(func(interface{}) {
			cts.Cancel()
		})
	}

	return cts
}

// NewCancellationTokenSourceWithoutParent creates a new cancellation token source without a parent
func NewCancellationTokenSourceWithoutParent() *CancellationTokenSource {
	return NewCancellationTokenSource(nil)
}

// Token returns the cancellation token
func (cts *CancellationTokenSource) Token() CancellationToken {
	cts.mu.Lock()
	defer cts.mu.Unlock()

	if cts.token == nil {
		// Create the token lazily when actually needed
		cts.token = NewMutableToken()
	}

	return cts.token
}

// Cancel cancels the token
func (cts *CancellationTokenSource) Cancel() {
	cts.mu.Lock()
	defer cts.mu.Unlock()

	if cts.token == nil {
		// Save an object by returning the default cancelled token when
		// cancellation happens before someone asks for the token
		cts.token = CancellationTokenCancelled
	} else if mutableToken, ok := cts.token.(*MutableToken); ok {
		// Actually cancel the mutable token
		mutableToken.Cancel()
	}
}

// Dispose disposes the cancellation token source
func (cts *CancellationTokenSource) Dispose(cancel bool) {
	if cancel {
		cts.Cancel()
	}

	if cts.parentListener != nil {
		cts.parentListener.Dispose()
		cts.parentListener = nil
	}

	cts.mu.Lock()
	defer cts.mu.Unlock()

	if cts.token == nil {
		// Ensure to initialize with an empty token if we had none
		cts.token = CancellationTokenNone
	} else if mutableToken, ok := cts.token.(*MutableToken); ok {
		// Actually dispose the mutable token
		mutableToken.Dispose()
	}
}

// CancelOnDispose creates a cancellation token that gets cancelled when the store is disposed
func CancelOnDispose(store *DisposableStore) CancellationToken {
	source := NewCancellationTokenSourceWithoutParent()
	store.Add(ToDisposable(func() {
		source.Cancel()
	}))
	return source.Token()
}

// CombinedCancellationToken creates a token that gets cancelled when any of the input tokens are cancelled
func CombinedCancellationToken(tokens ...CancellationToken) CancellationToken {
	if len(tokens) == 0 {
		return CancellationTokenNone
	}

	if len(tokens) == 1 {
		return tokens[0]
	}

	// Check if any token is already cancelled
	for _, token := range tokens {
		if token.IsCancellationRequested() {
			return CancellationTokenCancelled
		}
	}

	// Create a new source that gets cancelled when any input token is cancelled
	source := NewCancellationTokenSourceWithoutParent()

	for _, token := range tokens {
		if token != CancellationTokenNone {
			token.OnCancellationRequested().Subscribe(func(interface{}) {
				source.Cancel()
			})
		}
	}

	return source.Token()
}

// WithTimeout creates a cancellation token that gets cancelled after the specified timeout
func WithTimeout(timeout time.Duration, parent CancellationToken) CancellationToken {
	source := NewCancellationTokenSource(parent)

	go func() {
		select {
		case <-time.After(timeout):
			source.Cancel()
		}
	}()

	return source.Token()
}

// RaceCancellation races a channel against cancellation
func RaceCancellation[T any](ch <-chan T, token CancellationToken) <-chan T {
	result := make(chan T, 1)

	go func() {
		defer close(result)

		if token != nil && token.IsCancellationRequested() {
			return
		}

		// Set up cancellation listener
		var disposable IDisposable
		if token != nil {
			disposable = token.OnCancellationRequested().Subscribe(func(interface{}) {
				// Token was cancelled
			})
			defer disposable.Dispose()
		}

		// Wait for either the channel or cancellation
		select {
		case value, ok := <-ch:
			if ok {
				result <- value
			}
		default:
			// Non-blocking check
			if token != nil && token.IsCancellationRequested() {
				return
			}
			// Block for the value
			value, ok := <-ch
			if ok {
				result <- value
			}
		}
	}()

	return result
}

// CheckCancellation checks if the token is cancelled and panics with CancellationError if so
func CheckCancellation(token CancellationToken) {
	if token != nil && token.IsCancellationRequested() {
		panic(CancellationError{})
	}
}
