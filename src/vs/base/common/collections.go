/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

// IStringDictionary represents a dictionary with string keys
type IStringDictionary[V any] map[string]V

// INumberDictionary represents a dictionary with number keys
type INumberDictionary[V any] map[int]V

// GroupBy groups the collection into a dictionary based on the provided group function
func GroupBy[K comparable, V any](data []V, groupFn func(V) K) map[K][]V {
	result := make(map[K][]V)
	for _, element := range data {
		key := groupFn(element)
		result[key] = append(result[key], element)
	}
	return result
}

// GroupByMap groups the collection into a map based on the provided group function
func GroupByMap[K comparable, V any](data []V, groupFn func(V) K) map[K][]V {
	result := make(map[K][]V)
	for _, element := range data {
		key := groupFn(element)
		result[key] = append(result[key], element)
	}
	return result
}

// DiffSets computes the difference between two sets
func DiffSets[T comparable](before map[T]bool, after map[T]bool) (removed []T, added []T) {
	// Find removed items
	for item := range before {
		if !after[item] {
			removed = append(removed, item)
		}
	}

	// Find added items
	for item := range after {
		if !before[item] {
			added = append(added, item)
		}
	}

	return removed, added
}

// ForEach iterates over a map and calls the provided function for each key-value pair
func ForEach[K comparable, V any](m map[K]V, fn func(K, V)) {
	for key, value := range m {
		fn(key, value)
	}
}

// Keys returns all keys from a map
func Keys[K comparable, V any](m map[K]V) []K {
	keys := make([]K, 0, len(m))
	for key := range m {
		keys = append(keys, key)
	}
	return keys
}

// Values returns all values from a map
func Values[K comparable, V any](m map[K]V) []V {
	values := make([]V, 0, len(m))
	for _, value := range m {
		values = append(values, value)
	}
	return values
}

// ResourceMapEntry represents an entry in a ResourceMap
type ResourceMapEntry[T any] struct {
	URI   URI
	Value T
}

// ResourceMap is a map that uses URI as keys with custom comparison
type ResourceMap[T any] struct {
	entries map[string]*ResourceMapEntry[T]
	toKey   func(URI) string
}

// NewResourceMap creates a new ResourceMap
func NewResourceMap[T any]() *ResourceMap[T] {
	return &ResourceMap[T]{
		entries: make(map[string]*ResourceMapEntry[T]),
		toKey: func(uri URI) string {
			return uri.ToString()
		},
	}
}

// Set adds or updates an entry in the ResourceMap
func (rm *ResourceMap[T]) Set(uri URI, value T) {
	key := rm.toKey(uri)
	rm.entries[key] = &ResourceMapEntry[T]{
		URI:   uri,
		Value: value,
	}
}

// Get retrieves a value from the ResourceMap
func (rm *ResourceMap[T]) Get(uri URI) T {
	key := rm.toKey(uri)
	if entry, exists := rm.entries[key]; exists {
		return entry.Value
	}
	var zero T
	return zero
}

// Has checks if a URI exists in the ResourceMap
func (rm *ResourceMap[T]) Has(uri URI) bool {
	key := rm.toKey(uri)
	_, exists := rm.entries[key]
	return exists
}

// Delete removes an entry from the ResourceMap
func (rm *ResourceMap[T]) Delete(uri URI) bool {
	key := rm.toKey(uri)
	if _, exists := rm.entries[key]; exists {
		delete(rm.entries, key)
		return true
	}
	return false
}

// Clear removes all entries from the ResourceMap
func (rm *ResourceMap[T]) Clear() {
	rm.entries = make(map[string]*ResourceMapEntry[T])
}

// Size returns the number of entries in the ResourceMap
func (rm *ResourceMap[T]) Size() int {
	return len(rm.entries)
}

// ForEach iterates over all entries in the ResourceMap
func (rm *ResourceMap[T]) ForEach(fn func(URI, T)) {
	for _, entry := range rm.entries {
		fn(entry.URI, entry.Value)
	}
}

// Keys returns all URIs in the ResourceMap
func (rm *ResourceMap[T]) Keys() []URI {
	keys := make([]URI, 0, len(rm.entries))
	for _, entry := range rm.entries {
		keys = append(keys, entry.URI)
	}
	return keys
}

// Values returns all values in the ResourceMap
func (rm *ResourceMap[T]) Values() []T {
	values := make([]T, 0, len(rm.entries))
	for _, entry := range rm.entries {
		values = append(values, entry.Value)
	}
	return values
}

// URIRevive recreates a URI from UriComponents
func URIRevive(components map[string]interface{}) URI {
	scheme, _ := components["scheme"].(string)
	authority, _ := components["authority"].(string)
	path, _ := components["path"].(string)
	query, _ := components["query"].(string)
	fragment, _ := components["fragment"].(string)

	return *NewURI(scheme, authority, path, query, fragment)
}
