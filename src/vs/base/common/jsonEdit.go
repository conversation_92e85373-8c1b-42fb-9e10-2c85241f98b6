/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"encoding/json"
	"fmt"
	"strings"
)

// RemoveProperty removes a property from JSON text
func RemoveProperty(text string, path JSONPath, formattingOptions FormattingOptions) []Edit {
	return SetProperty(text, path, nil, formattingOptions, nil)
}

// SetProperty sets a property in JSON text
func SetProperty(text string, originalPath JSONPath, value interface{}, formattingOptions FormattingOptions, getInsertionIndex func([]string) int) []Edit {
	path := make(JSONPath, len(originalPath))
	copy(path, originalPath)

	errors := []ParseError{}
	root := ParseTree(text, &errors, nil)

	var parent Node
	var lastSegment Segment

	for len(path) > 0 {
		lastSegment = path[len(path)-1]
		path = path[:len(path)-1]
		parent = FindNodeAtLocation(root, path)

		if parent == nil && value != nil {
			if segmentStr, ok := lastSegment.(string); ok {
				value = map[string]interface{}{segmentStr: value}
			} else {
				value = []interface{}{value}
			}
		} else {
			break
		}
	}

	if parent == nil {
		// Empty document
		if value == nil {
			return []Edit{} // Property does not exist, nothing to do
		}

		content, _ := json.Marshal(value)
		offset := 0
		length := 0
		if root != nil {
			offset = root.GetOffset()
			length = root.GetLength()
		}

		return withFormatting(text, Edit{
			Offset:  offset,
			Length:  length,
			Content: string(content),
		}, formattingOptions)
	}

	if parent.GetType() == "object" {
		if segmentStr, ok := lastSegment.(string); ok {
			existing := FindNodeAtLocation(parent, JSONPath{segmentStr})

			if existing != nil {
				// Property exists
				if value == nil {
					// Delete property
					return removeObjectProperty(text, parent, segmentStr, formattingOptions)
				} else {
					// Update property
					return updateObjectProperty(text, existing, value, formattingOptions)
				}
			} else {
				// Property doesn't exist
				if value != nil {
					// Add property
					return addObjectProperty(text, parent, segmentStr, value, formattingOptions, getInsertionIndex)
				}
			}
		}
	} else if parent.GetType() == "array" {
		if segmentInt, ok := lastSegment.(int); ok {
			children := parent.GetChildren()

			if segmentInt >= 0 && segmentInt < len(children) {
				// Array element exists
				existing := children[segmentInt]
				if value == nil {
					// Remove array element
					return removeArrayElement(text, parent, segmentInt, formattingOptions)
				} else {
					// Update array element
					return updateArrayElement(text, existing, value, formattingOptions)
				}
			} else if value != nil {
				// Add array element
				return addArrayElement(text, parent, segmentInt, value, formattingOptions)
			}
		}
	}

	return []Edit{}
}

// withFormatting applies formatting to an edit
func withFormatting(text string, edit Edit, options FormattingOptions) []Edit {
	// Apply formatting to the edit content
	if edit.Content != "" {
		formatted := FormatJSON(edit.Content, nil, options)
		if len(formatted) > 0 {
			edit.Content = ApplyEdits(edit.Content, formatted)
		}
	}
	return []Edit{edit}
}

// removeObjectProperty removes a property from an object
func removeObjectProperty(text string, parent Node, propertyName string, options FormattingOptions) []Edit {
	children := parent.GetChildren()

	for i := 0; i < len(children); i += 2 {
		if i+1 < len(children) {
			// Check if this is the property to remove
			// In a real implementation, you'd extract the property name from the key node
			propertyNode := children[i+1]

			// Calculate removal range
			removeBegin := children[i].GetOffset()
			removeEnd := propertyNode.GetOffset() + propertyNode.GetLength()

			// Handle comma removal
			if i+2 < len(children) {
				// Not the last property, remove trailing comma
				removeEnd = children[i+2].GetOffset()
			} else if i > 0 {
				// Last property, remove preceding comma
				removeBegin = children[i-1].GetOffset() + children[i-1].GetLength()
			}

			return []Edit{{
				Offset:  removeBegin,
				Length:  removeEnd - removeBegin,
				Content: "",
			}}
		}
	}

	return []Edit{}
}

// updateObjectProperty updates a property in an object
func updateObjectProperty(text string, existing Node, value interface{}, options FormattingOptions) []Edit {
	content, _ := json.Marshal(value)

	return []Edit{{
		Offset:  existing.GetOffset(),
		Length:  existing.GetLength(),
		Content: string(content),
	}}
}

// addObjectProperty adds a property to an object
func addObjectProperty(text string, parent Node, propertyName string, value interface{}, options FormattingOptions, getInsertionIndex func([]string) int) []Edit {
	children := parent.GetChildren()

	// Create property content
	valueContent, _ := json.Marshal(value)
	propertyContent := fmt.Sprintf(`"%s": %s`, propertyName, string(valueContent))

	if len(children) == 0 {
		// Empty object
		return []Edit{{
			Offset:  parent.GetOffset() + 1, // After opening brace
			Length:  0,
			Content: propertyContent,
		}}
	}

	// Add comma and property
	lastChild := children[len(children)-1]
	insertOffset := lastChild.GetOffset() + lastChild.GetLength()

	return []Edit{{
		Offset:  insertOffset,
		Length:  0,
		Content: ", " + propertyContent,
	}}
}

// removeArrayElement removes an element from an array
func removeArrayElement(text string, parent Node, index int, options FormattingOptions) []Edit {
	children := parent.GetChildren()

	if index < 0 || index >= len(children) {
		return []Edit{}
	}

	element := children[index]
	removeBegin := element.GetOffset()
	removeEnd := element.GetOffset() + element.GetLength()

	// Handle comma removal
	if index < len(children)-1 {
		// Not the last element, include trailing comma/whitespace
		if index+1 < len(children) {
			removeEnd = children[index+1].GetOffset()
		}
	} else if index > 0 {
		// Last element, include preceding comma
		if index-1 >= 0 {
			removeBegin = children[index-1].GetOffset() + children[index-1].GetLength()
		}
	}

	return []Edit{{
		Offset:  removeBegin,
		Length:  removeEnd - removeBegin,
		Content: "",
	}}
}

// updateArrayElement updates an element in an array
func updateArrayElement(text string, existing Node, value interface{}, options FormattingOptions) []Edit {
	content, _ := json.Marshal(value)

	return []Edit{{
		Offset:  existing.GetOffset(),
		Length:  existing.GetLength(),
		Content: string(content),
	}}
}

// addArrayElement adds an element to an array
func addArrayElement(text string, parent Node, index int, value interface{}, options FormattingOptions) []Edit {
	children := parent.GetChildren()
	content, _ := json.Marshal(value)

	if len(children) == 0 {
		// Empty array
		return []Edit{{
			Offset:  parent.GetOffset() + 1, // After opening bracket
			Length:  0,
			Content: string(content),
		}}
	}

	if index >= len(children) {
		// Append to end
		lastChild := children[len(children)-1]
		insertOffset := lastChild.GetOffset() + lastChild.GetLength()

		return []Edit{{
			Offset:  insertOffset,
			Length:  0,
			Content: ", " + string(content),
		}}
	}

	// Insert at specific index
	targetChild := children[index]
	return []Edit{{
		Offset:  targetChild.GetOffset(),
		Length:  0,
		Content: string(content) + ", ",
	}}
}

// ParseTree parses JSON text and returns an AST
func ParseTree(text string, errors *[]ParseError, options *ParseOptions) Node {
	// This is a simplified implementation
	// A full implementation would build a complete AST

	if options == nil {
		options = &ParseOptionsDefault
	}

	// For now, return a simple root node
	return &simpleNode{
		nodeType: "object",
		offset:   0,
		length:   len(text),
		parent:   nil,
		children: []Node{},
	}
}

// simpleNode is a basic implementation of Node
type simpleNode struct {
	nodeType string
	offset   int
	length   int
	parent   Node
	children []Node
}

func (n *simpleNode) GetType() string     { return n.nodeType }
func (n *simpleNode) GetOffset() int      { return n.offset }
func (n *simpleNode) GetLength() int      { return n.length }
func (n *simpleNode) GetParent() Node     { return n.parent }
func (n *simpleNode) GetChildren() []Node { return n.children }

// IsValidJSON checks if the text is valid JSON
func IsValidJSON(text string) bool {
	var result interface{}
	return json.Unmarshal([]byte(text), &result) == nil
}

// StripComments removes comments from JSON text
func StripComments(text string, options *ParseOptions) string {
	if options == nil {
		options = &ParseOptionsDefault
	}

	if options.DisallowComments {
		return text
	}

	scanner := CreateScanner(text, false)
	result := strings.Builder{}
	lastOffset := 0

	for {
		token := scanner.Scan()
		if token == SyntaxKindEOF {
			break
		}

		offset := scanner.GetTokenOffset()

		if token == SyntaxKindLineCommentTrivia || token == SyntaxKindBlockCommentTrivia {
			// Add text before comment
			result.WriteString(text[lastOffset:offset])
			// Skip the comment
			lastOffset = offset + scanner.GetTokenLength()
		}
	}

	// Add remaining text
	result.WriteString(text[lastOffset:])

	return result.String()
}
