// Copyright (c) Microsoft Corporation. All rights reserved.
// Licensed under the MIT License. See License.txt in the project root for license information.

package common

import (
	"container/list"
	"sync"
)

type LRUCache[K comparable, V any] struct {
	capacity int
	list     *list.List
	elements map[K]*list.Element
	mutex    sync.Mutex
}

type entry[K comparable, V any] struct {
	key   K
	value V
}

func NewLRUCache[K comparable, V any](capacity int) *LRUCache[K, V] {
	return &LRUCache[K, V]{
		capacity: capacity,
		list:     list.New(),
		elements: make(map[K]*list.Element),
	}
}

func (c *LRUCache[K, V]) Get(key K) (V, bool) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if elem, ok := c.elements[key]; ok {
		c.list.MoveToFront(elem)
		return elem.Value.(*entry[K, V]).value, true
	}
	var zero V
	return zero, false
}

func (c *LRUCache[K, V]) Set(key K, value V) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if elem, ok := c.elements[key]; ok {
		c.list.MoveToFront(elem)
		elem.Value.(*entry[K, V]).value = value
		return
	}

	if c.list.Len() >= c.capacity {
		oldest := c.list.Back()
		if oldest != nil {
			delete(c.elements, oldest.Value.(*entry[K, V]).key)
			c.list.Remove(oldest)
		}
	}

	elem := c.list.PushFront(&entry[K, V]{key, value})
	c.elements[key] = elem
}

func (c *LRUCache[K, V]) Delete(key K) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if elem, ok := c.elements[key]; ok {
		delete(c.elements, key)
		c.list.Remove(elem)
	}
}

func (c *LRUCache[K, V]) Len() int {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	return c.list.Len()
}
