/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"reflect"
)

// IsString checks if the provided parameter is a string
func IsString(str interface{}) bool {
	_, ok := str.(string)
	return ok
}

// IsStringArray checks if the provided parameter is a string array
func IsStringArray(value interface{}) bool {
	v := reflect.ValueOf(value)
	if v.Kind() != reflect.Slice {
		return false
	}
	
	for i := 0; i < v.Len(); i++ {
		if !IsString(v.Index(i).Interface()) {
			return false
		}
	}
	return true
}

// IsObject checks if the provided parameter is an object (not null, array, regexp, or date)
func IsObject(obj interface{}) bool {
	if obj == nil {
		return false
	}
	
	v := reflect.ValueOf(obj)
	return v.Kind() == reflect.Struct || v.Kind() == reflect.Map
}

// IsNumber checks if the provided parameter is a number (and not NaN)
func IsNumber(obj interface{}) bool {
	switch obj.(type) {
	case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64, float32, float64:
		return true
	default:
		return false
	}
}

// IsBoolean checks if the provided parameter is a boolean
func IsBoolean(obj interface{}) bool {
	_, ok := obj.(bool)
	return ok
}

// IsUndefined checks if the provided parameter is nil (Go equivalent of undefined)
func IsUndefined(obj interface{}) bool {
	return obj == nil
}

// IsDefined checks if the provided parameter is not nil
func IsDefined(obj interface{}) bool {
	return obj != nil
}

// IsUndefinedOrNull checks if the provided parameter is nil
func IsUndefinedOrNull(obj interface{}) bool {
	return obj == nil
}

// Mutable represents a type that can be modified (Go doesn't have readonly, so this is just an alias)
type Mutable[T any] T

// AssertType panics if the condition is false
func AssertType(condition bool, message string) {
	if !condition {
		if message == "" {
			message = "Unexpected type"
		}
		panic(message)
	}
}

// Clone creates a deep copy of the given value (simplified implementation)
func Clone[T any](value T) T {
	// This is a simplified implementation
	// In a real implementation, you'd need proper deep cloning
	return value
}

// Equals checks if two values are equal
func Equals(a, b interface{}) bool {
	return reflect.DeepEqual(a, b)
}
