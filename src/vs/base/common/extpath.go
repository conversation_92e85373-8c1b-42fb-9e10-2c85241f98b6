package common

import (
	"math/rand"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// isRootOrDriveLetter returns true if the path is a root or drive letter
func isRootOrDriveLetter(path string) bool {
	if path == "" {
		return false
	}

	// Check for root path
	if path == "/" {
		return true
	}

	// Check for Windows drive letter (e.g. "C:")
	if len(path) == 2 && path[1] == ':' {
		return true
	}

	return false
}

// isEqualOrParent checks if child is equal to or a child of parent
func isEqualOrParent(child, parent string, ignoreCase bool) bool {
	if ignoreCase {
		child = strings.ToLower(child)
		parent = strings.ToLower(parent)
	}

	// Add trailing separator if needed
	if !strings.HasSuffix(parent, string(os.PathSeparator)) {
		parent += string(os.PathSeparator)
	}

	return child == parent || strings.HasPrefix(child, parent)
}

// randomPath generates a random path in the specified directory
func randomPath(dir string) string {
	rand.Seed(time.Now().UnixNano())
	const chars = "abcdefghijklmnopqrstuvwxyz0123456789"
	b := make([]byte, 8)
	for i := range b {
		b[i] = chars[rand.Intn(len(chars))]
	}
	return filepath.Join(dir, "vscode-tmp-"+string(b))
}

// toSlashes converts backslashes to forward slashes
func toSlashes(path string) string {
	return strings.ReplaceAll(path, "\\", "/")
}

// IsRootOrDriveLetter returns true if the path is a root or drive letter (public version)
func IsRootOrDriveLetter(path string) bool {
	return isRootOrDriveLetter(path)
}

// RandomPath generates a random path in the specified directory (public version)
func RandomPath(dir, prefix string, randomLength int) string {
	if prefix == "" && randomLength == 8 {
		return randomPath(dir)
	}

	// Custom implementation for different parameters
	rand.Seed(time.Now().UnixNano())
	const chars = "abcdefghijklmnopqrstuvwxyz0123456789"
	b := make([]byte, randomLength)
	for i := range b {
		b[i] = chars[rand.Intn(len(chars))]
	}

	name := prefix + string(b)
	return filepath.Join(dir, name)
}
