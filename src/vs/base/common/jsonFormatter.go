/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"strings"
)

// FormattingOptions represents options for JSON formatting
type FormattingOptions struct {
	// If indentation is based on spaces (insertSpaces = true), then what is the number of spaces that make an indent?
	TabSize *int `json:"tabSize,omitempty"`
	// Is indentation based on spaces?
	InsertSpaces *bool `json:"insertSpaces,omitempty"`
	// The default 'end of line' character. If not set, '\n' is used as default.
	Eol *string `json:"eol,omitempty"`
}

// Edit represents a text modification
type Edit struct {
	// The start offset of the modification
	Offset int `json:"offset"`
	// The length of the modification. Must not be negative. Empty length represents an *insert*
	Length int `json:"length"`
	// The new content. Empty content represents a *remove*
	Content string `json:"content"`
}

// Range represents a text range in the document
type Range struct {
	// The start offset of the range
	Offset int `json:"offset"`
	// The length of the range. Must not be negative
	Length int `json:"length"`
}

// DefaultFormattingOptions returns default formatting options
func DefaultFormattingOptions() FormattingOptions {
	tabSize := 4
	insertSpaces := true
	eol := "\n"
	return FormattingOptions{
		TabSize:      &tabSize,
		InsertSpaces: &insertSpaces,
		Eol:          &eol,
	}
}

// Format formats JSON text
func Format(documentText string, range_ *Range, options FormattingOptions) []Edit {
	if options.TabSize == nil {
		tabSize := 4
		options.TabSize = &tabSize
	}
	if options.InsertSpaces == nil {
		insertSpaces := true
		options.InsertSpaces = &insertSpaces
	}
	if options.Eol == nil {
		eol := "\n"
		options.Eol = &eol
	}

	scanner := CreateScanner(documentText, false)
	edits := []Edit{}
	
	var rangeStart, rangeEnd int
	if range_ != nil {
		rangeStart = range_.Offset
		rangeEnd = range_.Offset + range_.Length
	} else {
		rangeStart = 0
		rangeEnd = len(documentText)
	}

	// Simple formatting implementation
	// In a full implementation, you'd have a complete JSON formatter
	return formatJSON(documentText, rangeStart, rangeEnd, options)
}

// formatJSON performs basic JSON formatting
func formatJSON(text string, start, end int, options FormattingOptions) []Edit {
	edits := []Edit{}
	
	// This is a simplified implementation
	// A full implementation would parse the JSON and reformat it properly
	
	scanner := CreateScanner(text, false)
	indentLevel := 0
	needsIndent := false
	lastToken := SyntaxKindUnknown
	
	for {
		token := scanner.Scan()
		if token == SyntaxKindEOF {
			break
		}
		
		offset := scanner.GetTokenOffset()
		length := scanner.GetTokenLength()
		
		if offset < start || offset >= end {
			continue
		}
		
		switch token {
		case SyntaxKindOpenBraceToken, SyntaxKindOpenBracketToken:
			if needsIndent {
				edits = append(edits, Edit{
					Offset:  offset,
					Length:  0,
					Content: getIndent(indentLevel, options),
				})
				needsIndent = false
			}
			indentLevel++
			
		case SyntaxKindCloseBraceToken, SyntaxKindCloseBracketToken:
			indentLevel--
			if needsIndent {
				edits = append(edits, Edit{
					Offset:  offset,
					Length:  0,
					Content: getIndent(indentLevel, options),
				})
				needsIndent = false
			}
			
		case SyntaxKindCommaToken:
			// Add newline after comma
			edits = append(edits, Edit{
				Offset:  offset + length,
				Length:  0,
				Content: *options.Eol,
			})
			needsIndent = true
			
		case SyntaxKindColonToken:
			// Add space after colon
			if lastToken == SyntaxKindStringLiteral {
				edits = append(edits, Edit{
					Offset:  offset + length,
					Length:  0,
					Content: " ",
				})
			}
		}
		
		lastToken = token
	}
	
	return edits
}

// getIndent returns the indentation string for the given level
func getIndent(level int, options FormattingOptions) string {
	if level <= 0 {
		return ""
	}
	
	var indent string
	if *options.InsertSpaces {
		indent = strings.Repeat(" ", *options.TabSize)
	} else {
		indent = "\t"
	}
	
	return strings.Repeat(indent, level)
}

// IsEOL checks if a character is an end-of-line character
func IsEOL(ch byte) bool {
	return ch == '\n' || ch == '\r'
}

// ApplyEdits applies a list of edits to text
func ApplyEdits(text string, edits []Edit) string {
	if len(edits) == 0 {
		return text
	}
	
	// Sort edits by offset (descending) to apply from end to beginning
	sortedEdits := make([]Edit, len(edits))
	copy(sortedEdits, edits)
	
	// Simple bubble sort for small arrays
	for i := 0; i < len(sortedEdits)-1; i++ {
		for j := 0; j < len(sortedEdits)-i-1; j++ {
			if sortedEdits[j].Offset < sortedEdits[j+1].Offset {
				sortedEdits[j], sortedEdits[j+1] = sortedEdits[j+1], sortedEdits[j]
			}
		}
	}
	
	result := text
	for _, edit := range sortedEdits {
		if edit.Offset < 0 || edit.Offset > len(result) {
			continue
		}
		
		end := edit.Offset + edit.Length
		if end > len(result) {
			end = len(result)
		}
		
		result = result[:edit.Offset] + edit.Content + result[end:]
	}
	
	return result
}

// ModifyJSON modifies JSON text by applying edits
func ModifyJSON(text string, path JSONPath, value interface{}, options FormattingOptions) (string, []Edit) {
	// This is a simplified implementation
	// A full implementation would parse the JSON, modify the AST, and generate edits
	
	edits := []Edit{}
	
	// For now, just return the original text
	// In a real implementation, you would:
	// 1. Parse the JSON to get an AST
	// 2. Navigate to the specified path
	// 3. Modify the value at that path
	// 4. Generate edits to transform the original text
	
	return text, edits
}

// GetNodePath returns the JSON path to a node
func GetNodePath(node Node) JSONPath {
	path := JSONPath{}
	current := node
	
	for current != nil && current.GetParent() != nil {
		parent := current.GetParent()
		if parent.GetType() == "object" {
			// Find the property name
			children := parent.GetChildren()
			for i := 0; i < len(children); i += 2 { // properties come in pairs
				if i+1 < len(children) && children[i+1] == current {
					// This is a property value, get the key
					if children[i].GetType() == "string" {
						// Extract property name from string node
						path = append([]Segment{""}, path...) // placeholder
					}
					break
				}
			}
		} else if parent.GetType() == "array" {
			// Find the array index
			children := parent.GetChildren()
			for i, child := range children {
				if child == current {
					path = append([]Segment{i}, path...)
					break
				}
			}
		}
		current = parent
	}
	
	return path
}

// FindNodeAtLocation finds a node at the specified JSON path
func FindNodeAtLocation(root Node, path JSONPath) Node {
	current := root
	
	for _, segment := range path {
		if current == nil {
			return nil
		}
		
		switch current.GetType() {
		case "object":
			if segmentStr, ok := segment.(string); ok {
				children := current.GetChildren()
				for i := 0; i < len(children); i += 2 {
					if i+1 < len(children) {
						// Check if this property matches
						// In a real implementation, you'd extract the property name
						// from the string node and compare it
						if segmentStr == "" { // placeholder comparison
							current = children[i+1]
							break
						}
					}
				}
			} else {
				return nil
			}
			
		case "array":
			if segmentInt, ok := segment.(int); ok {
				children := current.GetChildren()
				if segmentInt >= 0 && segmentInt < len(children) {
					current = children[segmentInt]
				} else {
					return nil
				}
			} else {
				return nil
			}
			
		default:
			return nil
		}
	}
	
	return current
}

// GetLocation gets the location information for an offset in JSON text
func GetLocation(text string, position int) *Location {
	// This is a simplified implementation
	// A full implementation would parse the JSON and determine the exact location
	
	return &Location{
		PreviousNode:    nil,
		Path:            JSONPath{},
		IsAtPropertyKey: false,
	}
}
