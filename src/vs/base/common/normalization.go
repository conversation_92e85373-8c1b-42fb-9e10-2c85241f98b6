// Copyright (c) Microsoft Corporation. All rights reserved.
// Licensed under the MIT License. See License.txt in the project root for license information.

package common

import (
	"strings"
	"unicode"

	"golang.org/x/text/unicode/norm"
)

var (
	nfcCache *LRUCache[string, string] = NewLRUCache[string, string](10000) // bounded to 10000 elements
	nfdCache *LRUCache[string, string] = NewLRUCache[string, string](10000) // bounded to 10000 elements
)

func NormalizeNFC(str string) string {
	return normalize(str, norm.NFC, nfcCache)
}

func NormalizeNFD(str string) string {
	return normalize(str, norm.NFD, nfdCache)
}

func normalize(str string, form norm.Form, cache *LRUCache[string, string]) string {
	if str == "" {
		return str
	}

	if cached, ok := cache.Get(str); ok {
		return cached
	}

	var res string
	if hasNonASCII(str) {
		res = form.String(str)
	} else {
		res = str
	}

	cache.Set(str, res)
	return res
}

func hasNonASCII(s string) bool {
	for _, r := range s {
		if r > 0x80 {
			return true
		}
	}
	return false
}

func RemoveAccents(str string) string {
	// transform into NFD form and remove accents
	normalized := NormalizeNFD(str)
	var result strings.Builder
	for _, r := range normalized {
		if !unicode.Is(unicode.Mn, r) { // Mn: nonspacing marks
			result.WriteRune(r)
		}
	}
	return result.String()
}
