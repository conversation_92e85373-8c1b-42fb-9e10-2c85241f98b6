// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.

package node

import (
	"bufio"
	"errors"
	"os"
	"strings"

	"src/vs/base/common"
)

type ReleaseInfo struct {
	Id        string
	IdLike    string
	VersionId string
}

// getOSReleaseInfo extracts Linux release information from standard files.
// If the platform is macOS or Windows, it returns nil.
func GetOSReleaseInfo(errorLogger func(error)) (*ReleaseInfo, error) {
	if common.IsMacintosh || common.IsWindows {
		return nil, nil
	}

	paths := []string{"/etc/os-release", "/usr/lib/os-release", "/etc/lsb-release"}
	var file *os.File
	var err error
	for _, filePath := range paths {
		file, err = os.Open(filePath)
		if err == nil {
			defer file.Close()
			break
		}
	}
	if file == nil {
		errMsg := errors.New("Unable to retrieve release information from known identifier paths.")
		errorLogger(errMsg)
		return nil, nil
	}

	osReleaseKeys := map[string]struct{}{
		"ID":              {},
		"DISTRIB_ID":      {},
		"ID_LIKE":         {},
		"VERSION_ID":      {},
		"DISTRIB_RELEASE": {},
	}
	releaseInfo := &ReleaseInfo{Id: "unknown"}

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()
		if !strings.Contains(line, "=") {
			continue
		}
		parts := strings.SplitN(line, "=", 2)
		key := strings.ToUpper(strings.TrimSpace(parts[0]))
		if _, ok := osReleaseKeys[key]; ok {
			value := strings.ToLower(strings.Trim(strings.ReplaceAll(parts[1], "\"", ""), " "))
			if key == "ID" || key == "DISTRIB_ID" {
				releaseInfo.Id = value
			} else if key == "ID_LIKE" {
				releaseInfo.IdLike = value
			} else if key == "VERSION_ID" || key == "DISTRIB_RELEASE" {
				releaseInfo.VersionId = value
			}
		}
	}
	if err := scanner.Err(); err != nil {
		errorLogger(err)
		return nil, err
	}
	return releaseInfo, nil
}
