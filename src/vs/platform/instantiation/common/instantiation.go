/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"reflect"
	"sync"

	basecommon "src/vs/base/common"
)

// BrandedService represents a service with a brand for type safety
type BrandedService interface {
	ServiceBrand() interface{}
}

// ServiceIdentifier identifies a service of a specific type
type ServiceIdentifier[T any] interface {
	GetServiceID() string
}

// ServicesAccessor provides access to services
type ServicesAccessor interface {
	Get(id ServiceIdentifier[any]) interface{}
}

// IInstantiationService interface for dependency injection
type IInstantiationService interface {
	BrandedService

	// CreateInstance synchronously creates an instance
	CreateInstance(ctor interface{}, args ...interface{}) interface{}

	// InvokeFunction calls a function with a service accessor
	InvokeFunction(fn interface{}, args ...interface{}) interface{}

	// CreateChild creates a child service with additional services
	CreateChild(services map[ServiceIdentifier[any]]interface{}) IInstantiationService

	// Dispose disposes the instantiation service
	Dispose()
}

// ServiceCollection holds a collection of services
type ServiceCollection struct {
	services map[string]interface{}
	mutex    sync.RWMutex
}

// NewServiceCollection creates a new service collection
func NewServiceCollection() *ServiceCollection {
	return &ServiceCollection{
		services: make(map[string]interface{}),
	}
}

// Set adds a service to the collection
func (sc *ServiceCollection) Set(id ServiceIdentifier[any], service interface{}) {
	sc.mutex.Lock()
	defer sc.mutex.Unlock()
	sc.services[id.GetServiceID()] = service
}

// Get retrieves a service from the collection
func (sc *ServiceCollection) Get(id ServiceIdentifier[any]) interface{} {
	sc.mutex.RLock()
	defer sc.mutex.RUnlock()
	return sc.services[id.GetServiceID()]
}

// Has checks if a service exists in the collection
func (sc *ServiceCollection) Has(id ServiceIdentifier[any]) bool {
	sc.mutex.RLock()
	defer sc.mutex.RUnlock()
	_, exists := sc.services[id.GetServiceID()]
	return exists
}

// InstantiationService implements IInstantiationService
type InstantiationService struct {
	services *ServiceCollection
	parent   IInstantiationService
	children []IInstantiationService
	disposed bool
	mutex    sync.RWMutex
}

// NewInstantiationService creates a new instantiation service
func NewInstantiationService(services *ServiceCollection, parent IInstantiationService) *InstantiationService {
	return &InstantiationService{
		services: services,
		parent:   parent,
		children: make([]IInstantiationService, 0),
	}
}

// ServiceBrand implements BrandedService
func (is *InstantiationService) ServiceBrand() interface{} {
	return "instantiationService"
}

// CreateInstance creates an instance with dependency injection
func (is *InstantiationService) CreateInstance(ctor interface{}, args ...interface{}) interface{} {
	is.mutex.RLock()
	defer is.mutex.RUnlock()

	if is.disposed {
		panic("InstantiationService has been disposed")
	}

	// This is a simplified implementation
	// In a real implementation, you would analyze the constructor parameters
	// and inject the appropriate services
	ctorType := reflect.TypeOf(ctor)
	if ctorType.Kind() == reflect.Func {
		ctorValue := reflect.ValueOf(ctor)

		// Prepare arguments for the constructor
		var callArgs []reflect.Value
		for _, arg := range args {
			callArgs = append(callArgs, reflect.ValueOf(arg))
		}

		// Call the constructor
		results := ctorValue.Call(callArgs)
		if len(results) > 0 {
			return results[0].Interface()
		}
	}

	return nil
}

// InvokeFunction calls a function with service accessor
func (is *InstantiationService) InvokeFunction(fn interface{}, args ...interface{}) interface{} {
	is.mutex.RLock()
	defer is.mutex.RUnlock()

	if is.disposed {
		panic("InstantiationService has been disposed")
	}

	// Create a service accessor
	accessor := &serviceAccessor{services: is.services}

	fnValue := reflect.ValueOf(fn)
	if fnValue.Kind() == reflect.Func {
		var callArgs []reflect.Value
		callArgs = append(callArgs, reflect.ValueOf(accessor))

		for _, arg := range args {
			callArgs = append(callArgs, reflect.ValueOf(arg))
		}

		results := fnValue.Call(callArgs)
		if len(results) > 0 {
			return results[0].Interface()
		}
	}

	return nil
}

// CreateChild creates a child instantiation service
func (is *InstantiationService) CreateChild(services map[ServiceIdentifier[any]]interface{}) IInstantiationService {
	is.mutex.Lock()
	defer is.mutex.Unlock()

	childServices := NewServiceCollection()

	// Copy parent services
	for id, service := range is.services.services {
		childServices.services[id] = service
	}

	// Add new services
	for id, service := range services {
		childServices.Set(id, service)
	}

	child := NewInstantiationService(childServices, is)
	is.children = append(is.children, child)

	return child
}

// Dispose disposes the instantiation service
func (is *InstantiationService) Dispose() {
	is.mutex.Lock()
	defer is.mutex.Unlock()

	if is.disposed {
		return
	}

	// Dispose all children
	for _, child := range is.children {
		child.Dispose()
	}
	is.children = nil

	// Dispose services that implement basecommon.IDisposable
	for _, service := range is.services.services {
		if disposable, ok := service.(basecommon.IDisposable); ok {
			disposable.Dispose()
		}
	}

	is.disposed = true
}

// serviceAccessor implements ServicesAccessor
type serviceAccessor struct {
	services *ServiceCollection
}

func (sa *serviceAccessor) Get(id ServiceIdentifier[any]) interface{} {
	return sa.services.Get(id)
}

// CreateDecorator creates a service decorator (simplified implementation)
func CreateDecorator[T any](id string) ServiceIdentifier[T] {
	return &serviceIdentifier[T]{id: id}
}

type serviceIdentifier[T any] struct {
	id string
}

func (si *serviceIdentifier[T]) GetServiceID() string {
	return si.id
}

// Global instantiation service identifier
var IInstantiationServiceID = CreateDecorator[IInstantiationService]("instantiationService")
