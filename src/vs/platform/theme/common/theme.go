package common

// ColorScheme represents the color scheme of the theme.
type ColorScheme string

const (
	// Dark is the dark color scheme.
	Dark ColorScheme = "dark"
	// Light is the light color scheme.
	Light ColorScheme = "light"
	// HighContrastDark is the high contrast dark color scheme.
	HighContrastDark ColorScheme = "hcDark"
	// HighContrastLight is the high contrast light color scheme.
	HighContrastLight ColorScheme = "hcLight"
)

// ThemeTypeSelector represents the type of the theme.
type ThemeTypeSelector string

const (
	// VS is the Visual Studio theme.
	VS ThemeTypeSelector = "vs"
	// VSDark is the Visual Studio Dark theme.
	VSDark ThemeTypeSelector = "vs-dark"
	// HCBlack is the High Contrast Black theme.
	HCBlack ThemeTypeSelector = "hc-black"
	// HCLight is the High Contrast Light theme.
	HCLight ThemeTypeSelector = "hc-light"
)

// IsHighContrast returns true if the color scheme is high contrast.
func IsHighContrast(scheme ColorScheme) bool {
	return scheme == HighContrastDark || scheme == HighContrastLight
}

// IsDark returns true if the color scheme is dark.
func IsDark(scheme ColorScheme) bool {
	return scheme == Dark || scheme == HighContrastDark
}
