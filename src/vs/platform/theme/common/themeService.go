/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	basecommon "src/vs/base/common"
	envcommon "src/vs/platform/environment/common"
	instantiationcommon "src/vs/platform/instantiation/common"
)

// IThemeService is the theme service interface
var IThemeServiceId = instantiationcommon.CreateDecorator[IThemeService]("themeService")

// ColorIdentifier represents a color identifier
type ColorIdentifier = string

// ITokenStyle represents token styling information
type ITokenStyle struct {
	Foreground    *int  `json:"foreground,omitempty"`
	Bold          *bool `json:"bold,omitempty"`
	Underline     *bool `json:"underline,omitempty"`
	Strikethrough *bool `json:"strikethrough,omitempty"`
	Italic        *bool `json:"italic,omitempty"`
}

// IColorTheme represents a color theme
type IColorTheme interface {
	// GetType returns the theme type
	GetType() ColorScheme

	// GetLabel returns the theme label
	GetLabel() string

	// GetColor resolves the color of the given color identifier
	GetColor(color ColorIdentifier, useDefault ...bool) *string

	// Defines returns whether the theme defines a value for the color
	Defines(color ColorIdentifier) bool

	// GetTokenStyleMetadata returns the token style for a given classification
	GetTokenStyleMetadata(tokenType string, modifiers []string, modelLanguage string) *ITokenStyle

	// GetTokenColorMap returns list of all colors used with tokens
	GetTokenColorMap() []string

	// GetSemanticHighlighting returns whether semantic highlighting should be enabled
	GetSemanticHighlighting() bool
}

// IFileIconTheme represents a file icon theme
type IFileIconTheme interface {
	// HasFileIcons returns whether the theme has file icons
	HasFileIcons() bool

	// HasFolderIcons returns whether the theme has folder icons
	HasFolderIcons() bool

	// HidesExplorerArrows returns whether explorer arrows are hidden
	HidesExplorerArrows() bool
}

// IProductIconTheme represents a product icon theme
type IProductIconTheme interface {
	// GetIcon resolves the definition for the given icon
	GetIcon(iconContribution IconContribution) *IconDefinition
}

// ICssStyleCollector collects CSS rules
type ICssStyleCollector interface {
	AddRule(rule string)
}

// IThemingParticipant represents a theming participant
type IThemingParticipant func(theme IColorTheme, collector ICssStyleCollector, environment envcommon.IEnvironmentService)

// IThemeService provides theme management capabilities
type IThemeService interface {
	instantiationcommon.BrandedService

	// GetColorTheme returns the current color theme
	GetColorTheme() IColorTheme

	// OnDidColorThemeChange is fired when the color theme changes
	OnDidColorThemeChange() basecommon.Event[IColorTheme]

	// GetFileIconTheme returns the current file icon theme
	GetFileIconTheme() IFileIconTheme

	// OnDidFileIconThemeChange is fired when the file icon theme changes
	OnDidFileIconThemeChange() basecommon.Event[IFileIconTheme]

	// GetProductIconTheme returns the current product icon theme
	GetProductIconTheme() IProductIconTheme

	// OnDidProductIconThemeChange is fired when the product icon theme changes
	OnDidProductIconThemeChange() basecommon.Event[IProductIconTheme]
}

// IPartsSplash represents splash screen configuration
type IPartsSplash struct {
	ZoomLevel  *float64          `json:"zoomLevel,omitempty"`
	BaseTheme  ThemeTypeSelector `json:"baseTheme"`
	ColorInfo  ColorInfo         `json:"colorInfo"`
	LayoutInfo *LayoutInfo       `json:"layoutInfo,omitempty"`
}

// ColorInfo represents color information for splash screen
type ColorInfo struct {
	Background                  string  `json:"background"`
	Foreground                  *string `json:"foreground,omitempty"`
	EditorBackground            *string `json:"editorBackground,omitempty"`
	TitleBarBackground          *string `json:"titleBarBackground,omitempty"`
	TitleBarBorder              *string `json:"titleBarBorder,omitempty"`
	ActivityBarBackground       *string `json:"activityBarBackground,omitempty"`
	ActivityBarBorder           *string `json:"activityBarBorder,omitempty"`
	SideBarBackground           *string `json:"sideBarBackground,omitempty"`
	SideBarBorder               *string `json:"sideBarBorder,omitempty"`
	StatusBarBackground         *string `json:"statusBarBackground,omitempty"`
	StatusBarBorder             *string `json:"statusBarBorder,omitempty"`
	StatusBarNoFolderBackground *string `json:"statusBarNoFolderBackground,omitempty"`
	WindowBorder                *string `json:"windowBorder,omitempty"`
}

// LayoutInfo represents layout information for splash screen
type LayoutInfo struct {
	SideBarSide        string  `json:"sideBarSide"`
	EditorPartMinWidth int     `json:"editorPartMinWidth"`
	TitleBarHeight     int     `json:"titleBarHeight"`
	ActivityBarWidth   int     `json:"activityBarWidth"`
	SideBarWidth       int     `json:"sideBarWidth"`
	AuxiliaryBarWidth  int     `json:"auxiliaryBarWidth"`
	StatusBarHeight    int     `json:"statusBarHeight"`
	WindowBorder       bool    `json:"windowBorder"`
	WindowBorderRadius *string `json:"windowBorderRadius,omitempty"`
}

// GetThemeTypeSelector returns the theme type selector for a color scheme
func GetThemeTypeSelector(scheme ColorScheme) ThemeTypeSelector {
	switch scheme {
	case Dark:
		return VSDark
	case HighContrastDark:
		return HCBlack
	case HighContrastLight:
		return HCLight
	default:
		return VS
	}
}

// IconContribution represents an icon contribution
type IconContribution struct {
	ID string
}

// IconDefinition represents an icon definition
type IconDefinition struct {
	FontCharacter string
	FontID        string
}
