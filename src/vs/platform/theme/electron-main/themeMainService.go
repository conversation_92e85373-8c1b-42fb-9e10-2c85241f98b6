/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package electronmain

import (
	"src/vs/base/common"
	instantiationcommon "src/vs/platform/instantiation/common"
	themecommon "src/vs/platform/theme/common"
	windowcommon "src/vs/platform/window/common"
)

// IThemeMainServiceId is the main theme service identifier
var IThemeMainServiceId = instantiationcommon.CreateDecorator[IThemeMainService]("themeMainService")

// IWorkspaceIdentifier represents a workspace identifier (simplified)
type IWorkspaceIdentifier interface {
	GetID() string
}

// ISingleFolderWorkspaceIdentifier represents a single folder workspace identifier (simplified)
type ISingleFolderWorkspaceIdentifier interface {
	GetID() string
	GetURI() *common.URI
}

// IThemeMainService provides theme management capabilities for the main process
type IThemeMainService interface {
	instantiationcommon.BrandedService

	// OnDidChangeColorScheme is fired when the color scheme changes
	OnDidChangeColorScheme() common.Event[windowcommon.IColorScheme]

	// GetBackgroundColor returns the background color
	GetBackgroundColor() string

	// SaveWindowSplash saves the window splash configuration
	SaveWindowSplash(windowID *int, workspace interface{}, splash themecommon.IPartsSplash)

	// GetWindowSplash gets the window splash configuration
	GetWindowSplash(workspace interface{}) *themecommon.IPartsSplash

	// GetColorScheme returns the current color scheme
	GetColorScheme() windowcommon.IColorScheme
}

// ThemeMainService implements IThemeMainService
type ThemeMainService struct {
	onDidChangeColorScheme *common.Emitter[windowcommon.IColorScheme]
	backgroundColor        string
	colorScheme            windowcommon.IColorScheme
	windowSplashes         map[string]themecommon.IPartsSplash
}

// NewThemeMainService creates a new theme main service
func NewThemeMainService() *ThemeMainService {
	return &ThemeMainService{
		onDidChangeColorScheme: common.NewEmitter[windowcommon.IColorScheme](),
		backgroundColor:        "#ffffff",
		colorScheme: windowcommon.IColorScheme{
			Dark:         false,
			HighContrast: false,
		},
		windowSplashes: make(map[string]themecommon.IPartsSplash),
	}
}

// ServiceBrand implements BrandedService
func (t *ThemeMainService) ServiceBrand() interface{} {
	return nil
}

// OnDidChangeColorScheme returns the color scheme change event
func (t *ThemeMainService) OnDidChangeColorScheme() common.Event[windowcommon.IColorScheme] {
	return t.onDidChangeColorScheme.Event()
}

// GetBackgroundColor returns the current background color
func (t *ThemeMainService) GetBackgroundColor() string {
	return t.backgroundColor
}

// SaveWindowSplash saves the window splash configuration
func (t *ThemeMainService) SaveWindowSplash(windowID *int, workspace interface{}, splash themecommon.IPartsSplash) {
	key := t.getWorkspaceKey(workspace)
	if windowID != nil {
		// Include window ID in the key if provided
		key = key + "_" + string(rune(*windowID))
	}
	t.windowSplashes[key] = splash
}

// GetWindowSplash gets the window splash configuration
func (t *ThemeMainService) GetWindowSplash(workspace interface{}) *themecommon.IPartsSplash {
	key := t.getWorkspaceKey(workspace)
	if splash, exists := t.windowSplashes[key]; exists {
		return &splash
	}
	return nil
}

// GetColorScheme returns the current color scheme
func (t *ThemeMainService) GetColorScheme() windowcommon.IColorScheme {
	return t.colorScheme
}

// SetColorScheme sets the color scheme and fires the change event
func (t *ThemeMainService) SetColorScheme(scheme windowcommon.IColorScheme) {
	if t.colorScheme.Dark != scheme.Dark || t.colorScheme.HighContrast != scheme.HighContrast {
		t.colorScheme = scheme
		t.onDidChangeColorScheme.Fire(scheme)
	}
}

// SetBackgroundColor sets the background color
func (t *ThemeMainService) SetBackgroundColor(color string) {
	t.backgroundColor = color
}

// getWorkspaceKey generates a key for workspace identification
func (t *ThemeMainService) getWorkspaceKey(workspace interface{}) string {
	if workspace == nil {
		return "empty"
	}

	// Try to extract ID from workspace
	if w, ok := workspace.(IWorkspaceIdentifier); ok {
		return "workspace_" + w.GetID()
	}

	if w, ok := workspace.(ISingleFolderWorkspaceIdentifier); ok {
		return "folder_" + w.GetID()
	}

	// Fallback to string representation
	return "unknown"
}

// Dispose disposes the theme main service
func (t *ThemeMainService) Dispose() {
	if t.onDidChangeColorScheme != nil {
		t.onDidChangeColorScheme.Dispose()
	}
}
