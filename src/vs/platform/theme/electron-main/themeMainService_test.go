/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package electronmain

import (
	"testing"

	themecommon "src/vs/platform/theme/common"
	windowcommon "src/vs/platform/window/common"
)

func TestThemeMainServiceInstantiation(t *testing.T) {
	// Test service creation
	service := NewThemeMainService()
	if service == nil {
		t.Fatal("Failed to create ThemeMainService")
	}

	// Test ServiceBrand method
	brand := service.ServiceBrand()
	if brand != nil {
		t.<PERSON>("Expected ServiceBrand to return nil, got %v", brand)
	}
}

func TestThemeMainServiceMethods(t *testing.T) {
	service := NewThemeMainService()

	// Test GetBackgroundColor
	bgColor := service.GetBackgroundColor()
	if bgColor == "" {
		t.Error("GetBackgroundColor should return a non-empty string")
	}

	// Test GetColorScheme
	colorScheme := service.GetColorScheme()
	if colorScheme.Dark && colorScheme.HighContrast {
		t.Error("Default color scheme should not be both dark and high contrast")
	}

	// Test SetBackgroundColor
	newColor := "#000000"
	service.SetBackgroundColor(newColor)
	if service.GetBackgroundColor() != newColor {
		t.Errorf("Expected background color %s, got %s", newColor, service.GetBackgroundColor())
	}

	// Test SetColorScheme
	newScheme := windowcommon.IColorScheme{Dark: true, HighContrast: false}
	service.SetColorScheme(newScheme)
	resultScheme := service.GetColorScheme()
	if !resultScheme.Dark || resultScheme.HighContrast {
		t.Errorf("Expected dark scheme without high contrast, got %+v", resultScheme)
	}
}

func TestThemeMainServiceSplash(t *testing.T) {
	service := NewThemeMainService()

	// Test SaveWindowSplash and GetWindowSplash
	splash := themecommon.IPartsSplash{
		BaseTheme: themecommon.ThemeTypeSelectorVSDark,
		ColorInfo: themecommon.ColorInfo{
			Background: "#1e1e1e",
		},
	}

	// Test with nil workspace
	service.SaveWindowSplash(nil, nil, splash)
	retrievedSplash := service.GetWindowSplash(nil)
	if retrievedSplash == nil {
		t.Error("Expected to retrieve saved splash, got nil")
	}
	if retrievedSplash.BaseTheme != splash.BaseTheme {
		t.Errorf("Expected base theme %s, got %s", splash.BaseTheme, retrievedSplash.BaseTheme)
	}

	// Test with window ID
	windowID := 123
	service.SaveWindowSplash(&windowID, nil, splash)
	retrievedSplash = service.GetWindowSplash(nil)
	if retrievedSplash == nil {
		t.Error("Expected to retrieve saved splash with window ID, got nil")
	}
}

func TestThemeMainServiceEvents(t *testing.T) {
	service := NewThemeMainService()

	// Test event subscription
	event := service.OnDidChangeColorScheme()
	if event == nil {
		t.Error("OnDidChangeColorScheme should return a valid event")
	}

	// Test event firing
	eventFired := false
	var receivedScheme windowcommon.IColorScheme

	disposable := event.Subscribe(func(scheme windowcommon.IColorScheme) {
		eventFired = true
		receivedScheme = scheme
	})

	// Change color scheme to trigger event
	newScheme := windowcommon.IColorScheme{Dark: true, HighContrast: true}
	service.SetColorScheme(newScheme)

	if !eventFired {
		t.Error("Expected color scheme change event to fire")
	}
	if !receivedScheme.Dark || !receivedScheme.HighContrast {
		t.Errorf("Expected dark high contrast scheme, got %+v", receivedScheme)
	}

	// Clean up
	disposable.Dispose()
}

func TestThemeMainServiceDispose(t *testing.T) {
	service := NewThemeMainService()

	// Test disposal
	service.Dispose()

	// Verify emitter is disposed
	if service.onDidChangeColorScheme != nil && !service.onDidChangeColorScheme.IsDisposed() {
		t.Error("Expected emitter to be disposed")
	}
}
