/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"encoding/json"

	basecommon "src/vs/base/common"
	environmentcommon "src/vs/platform/environment/common"
	filescommon "src/vs/platform/files/common"
	logcommon "src/vs/platform/log/common"
	statenode "src/vs/platform/state/node"
	uriidentitycommon "src/vs/platform/uriIdentity/common"
	userDataProfileCommon "src/vs/platform/userDataProfile/common"
)

// StoredUserDataProfileState represents the stored state of a user data profile
type StoredUserDataProfileState struct {
	userDataProfileCommon.StoredUserDataProfile
	Location interface{} `json:"location"` // Can be URI or string
}

// UserDataProfilesReadonlyService provides read-only access to user data profiles
type UserDataProfilesReadonlyService struct {
	*userDataProfileCommon.UserDataProfilesService

	stateReadonlyService     statenode.IStateReadService
	nativeEnvironmentService environmentcommon.INativeEnvironmentService
}

// NewUserDataProfilesReadonlyService creates a new read-only user data profiles service
func NewUserDataProfilesReadonlyService(
	stateReadonlyService statenode.IStateReadService,
	uriIdentityService uriidentitycommon.IUriIdentityService,
	nativeEnvironmentService environmentcommon.INativeEnvironmentService,
	fileService filescommon.IFileService,
	logService logcommon.ILogService,
) *UserDataProfilesReadonlyService {
	service := &UserDataProfilesReadonlyService{
		stateReadonlyService:     stateReadonlyService,
		nativeEnvironmentService: nativeEnvironmentService,
	}

	// Initialize the base service
	service.UserDataProfilesService = userDataProfileCommon.NewUserDataProfilesService(
		nativeEnvironmentService,
		fileService,
		uriIdentityService,
		logService,
	)

	return service
}

// ServiceBrand implements the service brand
func (s *UserDataProfilesReadonlyService) ServiceBrand() interface{} {
	return "userDataProfilesReadonlyService"
}

// getStoredProfiles retrieves stored profiles from state
func (s *UserDataProfilesReadonlyService) getStoredProfiles() []*userDataProfileCommon.StoredUserDataProfile {
	profilesKey := "userDataProfiles"
	storedProfilesState := s.stateReadonlyService.GetItem(profilesKey, []StoredUserDataProfileState{})

	var storedProfiles []*userDataProfileCommon.StoredUserDataProfile

	// Convert the stored state to proper format
	if stateArray, ok := storedProfilesState.([]interface{}); ok {
		for _, item := range stateArray {
			if stateMap, ok := item.(map[string]interface{}); ok {
				// Convert map to StoredUserDataProfileState
				jsonData, err := json.Marshal(stateMap)
				if err != nil {
					continue
				}

				var state StoredUserDataProfileState
				if err := json.Unmarshal(jsonData, &state); err != nil {
					continue
				}

				// Convert location from string to URI if needed
				var location *basecommon.URI
				if locationStr, ok := state.Location.(string); ok {
					// Join with profiles home
					location = s.UriIdentityService().ExtUri().JoinPath(s.ProfilesHome(), locationStr)
				} else if locationMap, ok := state.Location.(map[string]interface{}); ok {
					// Revive URI from map
					location = reviveURI(locationMap)
				}

				if location != nil {
					storedProfile := &userDataProfileCommon.StoredUserDataProfile{
						Name:            state.Name,
						Location:        location,
						Icon:            state.Icon,
						UseDefaultFlags: state.UseDefaultFlags,
					}
					storedProfiles = append(storedProfiles, storedProfile)
				}
			}
		}
	}

	return storedProfiles
}

// getStoredProfileAssociations retrieves stored profile associations from state
func (s *UserDataProfilesReadonlyService) getStoredProfileAssociations() *userDataProfileCommon.StoredProfileAssociations {
	associationsKey := "profileAssociations"
	associations := s.stateReadonlyService.GetItem(associationsKey, &userDataProfileCommon.StoredProfileAssociations{})

	if assoc, ok := associations.(*userDataProfileCommon.StoredProfileAssociations); ok {
		return assoc
	}

	// Try to convert from map
	if assocMap, ok := associations.(map[string]interface{}); ok {
		jsonData, err := json.Marshal(assocMap)
		if err != nil {
			return &userDataProfileCommon.StoredProfileAssociations{}
		}

		var result userDataProfileCommon.StoredProfileAssociations
		if err := json.Unmarshal(jsonData, &result); err != nil {
			return &userDataProfileCommon.StoredProfileAssociations{}
		}

		return &result
	}

	return &userDataProfileCommon.StoredProfileAssociations{}
}

// getDefaultProfileExtensionsLocation returns the default profile extensions location
func (s *UserDataProfilesReadonlyService) getDefaultProfileExtensionsLocation() *basecommon.URI {
	extensionsPath := s.nativeEnvironmentService.ExtensionsPath()
	if extensionsPath == "" {
		return nil
	}

	extensionsURI := basecommon.File(extensionsPath)
	// Change scheme to match profiles home
	extensionsURI = extensionsURI.With(basecommon.UriComponents{
		Scheme: s.ProfilesHome().Scheme,
	})

	return s.UriIdentityService().ExtUri().JoinPath(extensionsURI, "extensions.json")
}

// UserDataProfilesService provides read-write access to user data profiles
type UserDataProfilesService struct {
	*UserDataProfilesReadonlyService

	stateService statenode.IStateService
}

// NewUserDataProfilesService creates a new user data profiles service
func NewUserDataProfilesService(
	stateService statenode.IStateService,
	uriIdentityService uriidentitycommon.IUriIdentityService,
	environmentService environmentcommon.INativeEnvironmentService,
	fileService filescommon.IFileService,
	logService logcommon.ILogService,
) *UserDataProfilesService {
	service := &UserDataProfilesService{
		stateService: stateService,
	}

	// Initialize the read-only service
	service.UserDataProfilesReadonlyService = NewUserDataProfilesReadonlyService(
		stateService,
		uriIdentityService,
		environmentService,
		fileService,
		logService,
	)

	return service
}

// ServiceBrand implements the service brand
func (s *UserDataProfilesService) ServiceBrand() interface{} {
	return "userDataProfilesService"
}

// saveStoredProfiles saves stored profiles to state
func (s *UserDataProfilesService) saveStoredProfiles(storedProfiles []*userDataProfileCommon.StoredUserDataProfile) {
	profilesKey := "userDataProfiles"

	if len(storedProfiles) > 0 {
		// Convert profiles to state format
		stateProfiles := make([]StoredUserDataProfileState, len(storedProfiles))
		for i, profile := range storedProfiles {
			stateProfiles[i] = StoredUserDataProfileState{
				StoredUserDataProfile: *profile,
				Location:              s.UriIdentityService().ExtUri().Basename(profile.Location),
			}
		}
		s.stateService.SetItem(profilesKey, stateProfiles)
	} else {
		s.stateService.RemoveItem(profilesKey)
	}
}

// saveStoredProfileAssociations saves stored profile associations to state
func (s *UserDataProfilesService) saveStoredProfileAssociations(storedProfileAssociations *userDataProfileCommon.StoredProfileAssociations) {
	associationsKey := "profileAssociations"

	if storedProfileAssociations.EmptyWindows != nil || storedProfileAssociations.Workspaces != nil {
		s.stateService.SetItem(associationsKey, storedProfileAssociations)
	} else {
		s.stateService.RemoveItem(associationsKey)
	}
}

// ServerUserDataProfilesService provides server-side user data profiles service
type ServerUserDataProfilesService struct {
	*UserDataProfilesService
}

// NewServerUserDataProfilesService creates a new server user data profiles service
func NewServerUserDataProfilesService(
	uriIdentityService uriidentitycommon.IUriIdentityService,
	environmentService environmentcommon.INativeEnvironmentService,
	fileService filescommon.IFileService,
	logService logcommon.ILogService,
) *ServerUserDataProfilesService {
	// Create a state service with immediate save strategy
	stateService := statenode.NewStateService(
		statenode.SaveStrategyImmediate,
		environmentService,
		logService,
		fileService,
	)

	service := &ServerUserDataProfilesService{}
	service.UserDataProfilesService = NewUserDataProfilesService(
		stateService,
		uriIdentityService,
		environmentService,
		fileService,
		logService,
	)

	return service
}

// ServiceBrand implements the service brand
func (s *ServerUserDataProfilesService) ServiceBrand() interface{} {
	return "serverUserDataProfilesService"
}

// Init initializes the server user data profiles service
func (s *ServerUserDataProfilesService) Init() error {
	// Initialize the state service first
	if stateService, ok := s.stateService.(*statenode.StateService); ok {
		if err := stateService.Init(); err != nil {
			return err
		}
	}

	// Then initialize the base service
	s.UserDataProfilesService.Init()
	return nil
}

// Helper function to revive URI from map
func reviveURI(uriMap map[string]interface{}) *basecommon.URI {
	scheme, _ := uriMap["scheme"].(string)
	authority, _ := uriMap["authority"].(string)
	path, _ := uriMap["path"].(string)
	query, _ := uriMap["query"].(string)
	fragment, _ := uriMap["fragment"].(string)

	return basecommon.From(basecommon.UriComponents{
		Scheme:    scheme,
		Authority: authority,
		Path:      path,
		Query:     query,
		Fragment:  fragment,
	})
}
