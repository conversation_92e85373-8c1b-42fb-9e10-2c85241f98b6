/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"fmt"
	"regexp"
	"sync"

	nls "src/vs"
	basecommon "src/vs/base/common"
	environmentcommon "src/vs/platform/environment/common"
	filescommon "src/vs/platform/files/common"
	instantiationcommon "src/vs/platform/instantiation/common"
	logcommon "src/vs/platform/log/common"
	uriidentitycommon "src/vs/platform/uriIdentity/common"
	workspacecommon "src/vs/platform/workspace/common"
)

// ProfileResourceType represents the type of profile resource
type ProfileResourceType string

const (
	ProfileResourceTypeSettings    ProfileResourceType = "settings"
	ProfileResourceTypeKeybindings ProfileResourceType = "keybindings"
	ProfileResourceTypeSnippets    ProfileResourceType = "snippets"
	ProfileResourceTypePrompts     ProfileResourceType = "prompts"
	ProfileResourceTypeTasks       ProfileResourceType = "tasks"
	ProfileResourceTypeExtensions  ProfileResourceType = "extensions"
	ProfileResourceTypeGlobalState ProfileResourceType = "globalState"
	ProfileResourceTypeMcp         ProfileResourceType = "mcp"
)

// UseDefaultProfileFlags indicates whether to use the default profile or not
type UseDefaultProfileFlags map[ProfileResourceType]bool

// ProfileResourceTypeFlags is an alias for UseDefaultProfileFlags
type ProfileResourceTypeFlags = UseDefaultProfileFlags

// IUserDataProfile represents a user data profile
type IUserDataProfile struct {
	ID                  string                  `json:"id"`
	IsDefault           bool                    `json:"isDefault"`
	Name                string                  `json:"name"`
	Icon                *string                 `json:"icon,omitempty"`
	Location            *basecommon.URI         `json:"location"`
	GlobalStorageHome   *basecommon.URI         `json:"globalStorageHome"`
	SettingsResource    *basecommon.URI         `json:"settingsResource"`
	KeybindingsResource *basecommon.URI         `json:"keybindingsResource"`
	TasksResource       *basecommon.URI         `json:"tasksResource"`
	SnippetsHome        *basecommon.URI         `json:"snippetsHome"`
	PromptsHome         *basecommon.URI         `json:"promptsHome"`
	ExtensionsResource  *basecommon.URI         `json:"extensionsResource"`
	McpResource         *basecommon.URI         `json:"mcpResource"`
	CacheHome           *basecommon.URI         `json:"cacheHome"`
	UseDefaultFlags     *UseDefaultProfileFlags `json:"useDefaultFlags,omitempty"`
	IsTransient         *bool                   `json:"isTransient,omitempty"`
	Workspaces          []*basecommon.URI       `json:"workspaces,omitempty"`
}

// IsUserDataProfile checks if the given object is a valid IUserDataProfile
func IsUserDataProfile(thing interface{}) bool {
	if thing == nil {
		return false
	}

	profile, ok := thing.(*IUserDataProfile)
	if !ok {
		return false
	}

	return profile != nil &&
		profile.ID != "" &&
		profile.Name != "" &&
		profile.Location != nil &&
		profile.GlobalStorageHome != nil &&
		profile.SettingsResource != nil &&
		profile.KeybindingsResource != nil &&
		profile.TasksResource != nil &&
		profile.SnippetsHome != nil &&
		profile.PromptsHome != nil &&
		profile.ExtensionsResource != nil &&
		profile.McpResource != nil
}

// DidChangeProfilesEvent represents the event when profiles change
type DidChangeProfilesEvent struct {
	Added   []*IUserDataProfile `json:"added"`
	Removed []*IUserDataProfile `json:"removed"`
	Updated []*IUserDataProfile `json:"updated"`
	All     []*IUserDataProfile `json:"all"`
}

// WillCreateProfileEvent represents the event before creating a profile
type WillCreateProfileEvent struct {
	Profile *IUserDataProfile
	Join    func(promise chan error)
}

// WillRemoveProfileEvent represents the event before removing a profile
type WillRemoveProfileEvent struct {
	Profile *IUserDataProfile
	Join    func(promise chan error)
}

// IUserDataProfileOptions represents options for creating a user data profile
type IUserDataProfileOptions struct {
	Icon            *string                 `json:"icon,omitempty"`
	UseDefaultFlags *UseDefaultProfileFlags `json:"useDefaultFlags,omitempty"`
	Transient       *bool                   `json:"transient,omitempty"`
	Workspaces      []*basecommon.URI       `json:"workspaces,omitempty"`
}

// IUserDataProfileUpdateOptions represents options for updating a user data profile
type IUserDataProfileUpdateOptions struct {
	Name            *string                 `json:"name,omitempty"`
	Icon            *string                 `json:"icon,omitempty"`
	UseDefaultFlags *UseDefaultProfileFlags `json:"useDefaultFlags,omitempty"`
	Transient       *bool                   `json:"transient,omitempty"`
	Workspaces      []*basecommon.URI       `json:"workspaces,omitempty"`
}

// IUserDataProfilesService represents the user data profiles service interface
type IUserDataProfilesService interface {
	// Service brand for type safety
	ServiceBrand() interface{}

	// Properties
	ProfilesHome() *basecommon.URI
	DefaultProfile() *IUserDataProfile
	Profiles() []*IUserDataProfile

	// Events
	OnDidChangeProfiles() basecommon.Event[*DidChangeProfilesEvent]
	OnDidResetWorkspaces() basecommon.Event[interface{}]

	// Methods
	CreateNamedProfile(name string, options *IUserDataProfileOptions, workspaceIdentifier workspacecommon.IAnyWorkspaceIdentifier) (*IUserDataProfile, error)
	CreateTransientProfile(workspaceIdentifier workspacecommon.IAnyWorkspaceIdentifier) (*IUserDataProfile, error)
	CreateProfile(id string, name string, options *IUserDataProfileOptions, workspaceIdentifier workspacecommon.IAnyWorkspaceIdentifier) (*IUserDataProfile, error)
	UpdateProfile(profile *IUserDataProfile, options *IUserDataProfileUpdateOptions) (*IUserDataProfile, error)
	RemoveProfile(profile *IUserDataProfile) error

	SetProfileForWorkspace(workspaceIdentifier workspacecommon.IAnyWorkspaceIdentifier, profile *IUserDataProfile) error
	ResetWorkspaces() error

	CleanUp() error
	CleanUpTransientProfiles() error
}

// ReviveProfile revives a profile from a DTO with the given scheme
func ReviveProfile(profile map[string]interface{}, scheme string) *IUserDataProfile {
	// Helper function to revive URI with scheme
	reviveURI := func(uriData interface{}) *basecommon.URI {
		if uriData == nil {
			return nil
		}
		if uriMap, ok := uriData.(map[string]interface{}); ok {
			uri := basecommon.From(basecommon.UriComponents{
				Scheme:    getString(uriMap, "scheme"),
				Authority: getString(uriMap, "authority"),
				Path:      getString(uriMap, "path"),
				Query:     getString(uriMap, "query"),
				Fragment:  getString(uriMap, "fragment"),
			})
			return uri.With(basecommon.UriComponents{Scheme: scheme})
		}
		return nil
	}

	// Helper function to revive URI array
	reviveURIArray := func(uriArrayData interface{}) []*basecommon.URI {
		if uriArrayData == nil {
			return nil
		}
		if uriArray, ok := uriArrayData.([]interface{}); ok {
			result := make([]*basecommon.URI, len(uriArray))
			for i, uriData := range uriArray {
				result[i] = reviveURI(uriData)
			}
			return result
		}
		return nil
	}

	return &IUserDataProfile{
		ID:                  getString(profile, "id"),
		IsDefault:           getBool(profile, "isDefault"),
		Name:                getString(profile, "name"),
		Icon:                getStringPtr(profile, "icon"),
		Location:            reviveURI(profile["location"]),
		GlobalStorageHome:   reviveURI(profile["globalStorageHome"]),
		SettingsResource:    reviveURI(profile["settingsResource"]),
		KeybindingsResource: reviveURI(profile["keybindingsResource"]),
		TasksResource:       reviveURI(profile["tasksResource"]),
		SnippetsHome:        reviveURI(profile["snippetsHome"]),
		PromptsHome:         reviveURI(profile["promptsHome"]),
		ExtensionsResource:  reviveURI(profile["extensionsResource"]),
		McpResource:         reviveURI(profile["mcpResource"]),
		CacheHome:           reviveURI(profile["cacheHome"]),
		UseDefaultFlags:     getUseDefaultFlags(profile, "useDefaultFlags"),
		IsTransient:         getBoolPtr(profile, "isTransient"),
		Workspaces:          reviveURIArray(profile["workspaces"]),
	}
}

// Helper functions for type conversion
func getString(m map[string]interface{}, key string) string {
	if val, ok := m[key]; ok {
		if str, ok := val.(string); ok {
			return str
		}
	}
	return ""
}

func getBool(m map[string]interface{}, key string) bool {
	if val, ok := m[key]; ok {
		if b, ok := val.(bool); ok {
			return b
		}
	}
	return false
}

func getStringPtr(m map[string]interface{}, key string) *string {
	if val, ok := m[key]; ok {
		if str, ok := val.(string); ok {
			return &str
		}
	}
	return nil
}

func getBoolPtr(m map[string]interface{}, key string) *bool {
	if val, ok := m[key]; ok {
		if b, ok := val.(bool); ok {
			return &b
		}
	}
	return nil
}

func getUseDefaultFlags(m map[string]interface{}, key string) *UseDefaultProfileFlags {
	if val, ok := m[key]; ok {
		if flagsMap, ok := val.(map[string]interface{}); ok {
			flags := make(UseDefaultProfileFlags)
			for k, v := range flagsMap {
				if b, ok := v.(bool); ok {
					flags[ProfileResourceType(k)] = b
				}
			}
			return &flags
		}
	}
	return nil
}

// ToUserDataProfile creates a user data profile from the given parameters
func ToUserDataProfile(id, name string, location, profilesCacheHome *basecommon.URI, options *IUserDataProfileOptions, defaultProfile *IUserDataProfile) *IUserDataProfile {
	profile := &IUserDataProfile{
		ID:        id,
		Name:      name,
		Location:  location,
		IsDefault: false,
		CacheHome: basecommon.JoinPath(profilesCacheHome, id),
	}

	if options != nil {
		profile.Icon = options.Icon
		profile.UseDefaultFlags = options.UseDefaultFlags
		profile.IsTransient = options.Transient
		profile.Workspaces = options.Workspaces
	}

	// Set resource paths based on default flags
	if defaultProfile != nil && options != nil && options.UseDefaultFlags != nil {
		if (*options.UseDefaultFlags)[ProfileResourceTypeGlobalState] {
			profile.GlobalStorageHome = defaultProfile.GlobalStorageHome
		} else {
			profile.GlobalStorageHome = basecommon.JoinPath(location, "globalStorage")
		}

		if (*options.UseDefaultFlags)[ProfileResourceTypeSettings] {
			profile.SettingsResource = defaultProfile.SettingsResource
		} else {
			profile.SettingsResource = basecommon.JoinPath(location, "settings.json")
		}

		if (*options.UseDefaultFlags)[ProfileResourceTypeKeybindings] {
			profile.KeybindingsResource = defaultProfile.KeybindingsResource
		} else {
			profile.KeybindingsResource = basecommon.JoinPath(location, "keybindings.json")
		}

		if (*options.UseDefaultFlags)[ProfileResourceTypeTasks] {
			profile.TasksResource = defaultProfile.TasksResource
		} else {
			profile.TasksResource = basecommon.JoinPath(location, "tasks.json")
		}

		if (*options.UseDefaultFlags)[ProfileResourceTypeSnippets] {
			profile.SnippetsHome = defaultProfile.SnippetsHome
		} else {
			profile.SnippetsHome = basecommon.JoinPath(location, "snippets")
		}

		if (*options.UseDefaultFlags)[ProfileResourceTypePrompts] {
			profile.PromptsHome = defaultProfile.PromptsHome
		} else {
			profile.PromptsHome = basecommon.JoinPath(location, "prompts")
		}

		if (*options.UseDefaultFlags)[ProfileResourceTypeExtensions] {
			profile.ExtensionsResource = defaultProfile.ExtensionsResource
		} else {
			profile.ExtensionsResource = basecommon.JoinPath(location, "extensions.json")
		}

		if (*options.UseDefaultFlags)[ProfileResourceTypeMcp] {
			profile.McpResource = defaultProfile.McpResource
		} else {
			profile.McpResource = basecommon.JoinPath(location, "mcp.json")
		}
	} else {
		// Default paths when no default profile or flags
		profile.GlobalStorageHome = basecommon.JoinPath(location, "globalStorage")
		profile.SettingsResource = basecommon.JoinPath(location, "settings.json")
		profile.KeybindingsResource = basecommon.JoinPath(location, "keybindings.json")
		profile.TasksResource = basecommon.JoinPath(location, "tasks.json")
		profile.SnippetsHome = basecommon.JoinPath(location, "snippets")
		profile.PromptsHome = basecommon.JoinPath(location, "prompts")
		profile.ExtensionsResource = basecommon.JoinPath(location, "extensions.json")
		profile.McpResource = basecommon.JoinPath(location, "mcp.json")
	}

	return profile
}

// UserDataProfilesObject represents the profiles object
type UserDataProfilesObject struct {
	Profiles     []*IUserDataProfile          `json:"profiles"`
	EmptyWindows map[string]*IUserDataProfile `json:"emptyWindows"`
}

// StoredUserDataProfile represents a stored user data profile
type StoredUserDataProfile struct {
	Name            string                  `json:"name"`
	Location        *basecommon.URI         `json:"location"`
	Icon            *string                 `json:"icon,omitempty"`
	UseDefaultFlags *UseDefaultProfileFlags `json:"useDefaultFlags,omitempty"`
}

// StoredProfileAssociations represents stored profile associations
type StoredProfileAssociations struct {
	Workspaces   basecommon.IStringDictionary[string] `json:"workspaces,omitempty"`
	EmptyWindows basecommon.IStringDictionary[string] `json:"emptyWindows,omitempty"`
}

// UserDataProfilesService implements IUserDataProfilesService
type UserDataProfilesService struct {
	basecommon.Disposable

	// Constants
	profilesKey            string
	profileAssociationsKey string

	// Properties
	profilesHome      *basecommon.URI
	profilesCacheHome *basecommon.URI

	// Services
	environmentService environmentcommon.IEnvironmentService
	fileService        filescommon.IFileService
	uriIdentityService uriidentitycommon.IUriIdentityService
	logService         logcommon.ILogService

	// Events
	onDidChangeProfiles  basecommon.Emitter[*DidChangeProfilesEvent]
	onWillCreateProfile  basecommon.Emitter[*WillCreateProfileEvent]
	onWillRemoveProfile  basecommon.Emitter[*WillRemoveProfileEvent]
	onDidResetWorkspaces basecommon.Emitter[interface{}]

	// State
	profilesObject          *UserDataProfilesObject
	transientProfilesObject *UserDataProfilesObject
	profileCreationPromises map[string]chan *IUserDataProfile
	mutex                   sync.RWMutex
}

// NewUserDataProfilesService creates a new UserDataProfilesService
func NewUserDataProfilesService(
	environmentService environmentcommon.IEnvironmentService,
	fileService filescommon.IFileService,
	uriIdentityService uriidentitycommon.IUriIdentityService,
	logService logcommon.ILogService,
) *UserDataProfilesService {
	service := &UserDataProfilesService{
		profilesKey:             "userDataProfiles",
		profileAssociationsKey:  "profileAssociations",
		environmentService:      environmentService,
		fileService:             fileService,
		uriIdentityService:      uriIdentityService,
		logService:              logService,
		profileCreationPromises: make(map[string]chan *IUserDataProfile),
		transientProfilesObject: &UserDataProfilesObject{
			Profiles:     make([]*IUserDataProfile, 0),
			EmptyWindows: make(map[string]*IUserDataProfile),
		},
	}

	service.profilesHome = basecommon.JoinPath(environmentService.UserRoamingDataHome(), "profiles")
	// For now, use UserRoamingDataHome as cache home - this should be updated when CacheHome is available
	service.profilesCacheHome = basecommon.JoinPath(environmentService.UserRoamingDataHome(), "CachedProfilesData")

	// Initialize events
	service.onDidChangeProfiles = *basecommon.NewEmitter[*DidChangeProfilesEvent]()
	service.onWillCreateProfile = *basecommon.NewEmitter[*WillCreateProfileEvent]()
	service.onWillRemoveProfile = *basecommon.NewEmitter[*WillRemoveProfileEvent]()
	service.onDidResetWorkspaces = *basecommon.NewEmitter[interface{}]()

	return service
}

// ServiceBrand implements the service brand
func (ups *UserDataProfilesService) ServiceBrand() interface{} {
	return "userDataProfilesService"
}

// UriIdentityService returns the URI identity service
func (s *UserDataProfilesService) UriIdentityService() uriidentitycommon.IUriIdentityService {
	return s.uriIdentityService
}

// ProfilesHome returns the profiles home directory
func (ups *UserDataProfilesService) ProfilesHome() *basecommon.URI {
	return ups.profilesHome
}

// DefaultProfile returns the default profile
func (ups *UserDataProfilesService) DefaultProfile() *IUserDataProfile {
	profiles := ups.Profiles()
	if len(profiles) > 0 {
		return profiles[0]
	}
	return nil
}

// Profiles returns all profiles (persistent + transient)
func (ups *UserDataProfilesService) Profiles() []*IUserDataProfile {
	ups.mutex.RLock()
	defer ups.mutex.RUnlock()

	profilesObj := ups.getProfilesObject()
	result := make([]*IUserDataProfile, 0, len(profilesObj.Profiles)+len(ups.transientProfilesObject.Profiles))
	result = append(result, profilesObj.Profiles...)
	result = append(result, ups.transientProfilesObject.Profiles...)
	return result
}

// OnDidChangeProfiles returns the event for profile changes
func (ups *UserDataProfilesService) OnDidChangeProfiles() basecommon.Event[*DidChangeProfilesEvent] {
	return ups.onDidChangeProfiles.Event()
}

// OnDidResetWorkspaces returns the event for workspace resets
func (ups *UserDataProfilesService) OnDidResetWorkspaces() basecommon.Event[interface{}] {
	return ups.onDidResetWorkspaces.Event()
}

// Init initializes the service
func (ups *UserDataProfilesService) Init() {
	ups.mutex.Lock()
	defer ups.mutex.Unlock()
	ups.profilesObject = nil
}

// getProfilesObject returns the profiles object, creating it if necessary
func (ups *UserDataProfilesService) getProfilesObject() *UserDataProfilesObject {
	if ups.profilesObject == nil {
		defaultProfile := ups.createDefaultProfile()
		profiles := []*IUserDataProfile{defaultProfile}

		// Load stored profiles
		storedProfiles := ups.getStoredProfiles()
		for _, storedProfile := range storedProfiles {
			if storedProfile.Name == "" || storedProfile.Location == nil {
				ups.logService.Warn("Skipping invalid stored profile", storedProfile.Location, storedProfile.Name)
				continue
			}

			profile := ToUserDataProfile(
				basecommon.URIBasename(storedProfile.Location),
				storedProfile.Name,
				storedProfile.Location,
				ups.profilesCacheHome,
				&IUserDataProfileOptions{
					Icon:            storedProfile.Icon,
					UseDefaultFlags: storedProfile.UseDefaultFlags,
				},
				defaultProfile,
			)
			profiles = append(profiles, profile)
		}

		emptyWindows := make(map[string]*IUserDataProfile)

		// Load profile associations
		associations := ups.getStoredProfileAssociations()
		if associations.Workspaces != nil {
			for workspacePath, profileID := range associations.Workspaces {
				workspace, err := basecommon.ParseURI(workspacePath)
				if err != nil {
					ups.logService.Warn("Failed to parse workspace URI", workspacePath, err)
					continue
				}
				for _, profile := range profiles {
					if profile.ID == profileID {
						if profile.Workspaces == nil {
							profile.Workspaces = make([]*basecommon.URI, 0)
						}
						profile.Workspaces = append(profile.Workspaces, workspace)
						break
					}
				}
			}
		}

		if associations.EmptyWindows != nil {
			for windowID, profileID := range associations.EmptyWindows {
				for _, profile := range profiles {
					if profile.ID == profileID {
						emptyWindows[windowID] = profile
						break
					}
				}
			}
		}

		ups.profilesObject = &UserDataProfilesObject{
			Profiles:     profiles,
			EmptyWindows: emptyWindows,
		}
	}
	return ups.profilesObject
}

// createDefaultProfile creates the default profile
func (ups *UserDataProfilesService) createDefaultProfile() *IUserDataProfile {
	defaultProfile := ToUserDataProfile(
		"__default__profile__",
		nls.Localize("defaultProfile", "Default"),
		ups.environmentService.UserRoamingDataHome(),
		ups.profilesCacheHome,
		nil,
		nil,
	)

	// Set default profile properties
	defaultProfile.IsDefault = true

	// Get default extensions location if available
	if defaultExtensionsLocation := ups.getDefaultProfileExtensionsLocation(); defaultExtensionsLocation != nil {
		defaultProfile.ExtensionsResource = defaultExtensionsLocation
	}

	return defaultProfile
}

// Placeholder methods that need to be implemented by concrete implementations
func (ups *UserDataProfilesService) getStoredProfiles() []*StoredUserDataProfile {
	// This should be implemented by concrete implementations
	return []*StoredUserDataProfile{}
}

func (ups *UserDataProfilesService) saveStoredProfiles(profiles []*StoredUserDataProfile) {
	// This should be implemented by concrete implementations
	panic("not implemented")
}

func (ups *UserDataProfilesService) getStoredProfileAssociations() *StoredProfileAssociations {
	// This should be implemented by concrete implementations
	return &StoredProfileAssociations{}
}

func (ups *UserDataProfilesService) saveStoredProfileAssociations(associations *StoredProfileAssociations) {
	// This should be implemented by concrete implementations
	panic("not implemented")
}

func (ups *UserDataProfilesService) getDefaultProfileExtensionsLocation() *basecommon.URI {
	// This should be implemented by concrete implementations
	return nil
}

// CreateTransientProfile creates a transient profile
func (ups *UserDataProfilesService) CreateTransientProfile(workspaceIdentifier workspacecommon.IAnyWorkspaceIdentifier) (*IUserDataProfile, error) {
	namePrefix := "Temp"
	nameRegex := regexp.MustCompile(fmt.Sprintf(`%s\s(\d+)`, regexp.QuoteMeta(namePrefix)))
	nameIndex := 0

	for _, profile := range ups.Profiles() {
		matches := nameRegex.FindStringSubmatch(profile.Name)
		if len(matches) > 1 {
			var index int
			fmt.Sscanf(matches[1], "%d", &index)
			if index > nameIndex {
				nameIndex = index
			}
		}
	}

	name := fmt.Sprintf("%s %d", namePrefix, nameIndex+1)
	id := fmt.Sprintf("%x", basecommon.Hash(basecommon.GenerateUuid()))

	return ups.CreateProfile(id, name, &IUserDataProfileOptions{Transient: &[]bool{true}[0]}, workspaceIdentifier)
}

// CreateNamedProfile creates a named profile
func (ups *UserDataProfilesService) CreateNamedProfile(name string, options *IUserDataProfileOptions, workspaceIdentifier workspacecommon.IAnyWorkspaceIdentifier) (*IUserDataProfile, error) {
	id := fmt.Sprintf("%x", basecommon.Hash(basecommon.GenerateUuid()))
	return ups.CreateProfile(id, name, options, workspaceIdentifier)
}

// CreateProfile creates a profile with the given parameters
func (ups *UserDataProfilesService) CreateProfile(id, name string, options *IUserDataProfileOptions, workspaceIdentifier workspacecommon.IAnyWorkspaceIdentifier) (*IUserDataProfile, error) {
	return ups.doCreateProfile(id, name, options, workspaceIdentifier)
}

// doCreateProfile performs the actual profile creation
func (ups *UserDataProfilesService) doCreateProfile(id, name string, options *IUserDataProfileOptions, workspaceIdentifier workspacecommon.IAnyWorkspaceIdentifier) (*IUserDataProfile, error) {
	if !basecommon.IsString(name) || name == "" {
		return nil, fmt.Errorf("name of the profile is mandatory and must be of type string")
	}

	ups.mutex.Lock()
	defer ups.mutex.Unlock()

	// Check if profile creation is already in progress
	if promise, exists := ups.profileCreationPromises[name]; exists {
		return <-promise, nil
	}

	// Create promise for this profile creation
	promise := make(chan *IUserDataProfile, 1)
	ups.profileCreationPromises[name] = promise

	go func() {
		defer func() {
			ups.mutex.Lock()
			delete(ups.profileCreationPromises, name)
			ups.mutex.Unlock()
		}()

		// Check if profile already exists
		for _, existing := range ups.Profiles() {
			existingIsTransient := existing.IsTransient != nil && *existing.IsTransient
			optionsIsTransient := options != nil && options.Transient != nil && *options.Transient
			if existing.ID == id || (!existingIsTransient && !optionsIsTransient && existing.Name == name) {
				promise <- nil
				return
			}
		}

		// Handle workspace identifier
		var workspace interface{}
		if workspaceIdentifier != nil {
			workspace = ups.getWorkspace(workspaceIdentifier)
		}

		// Add workspace to options if it's a URI
		if workspace != nil {
			if workspaceURI, ok := workspace.(*basecommon.URI); ok {
				if options == nil {
					options = &IUserDataProfileOptions{}
				}
				options.Workspaces = []*basecommon.URI{workspaceURI}
			}
		}

		// Create the profile
		profile := ToUserDataProfile(id, name, basecommon.JoinPath(ups.profilesHome, id), ups.profilesCacheHome, options, ups.DefaultProfile())

		// Create profile directory
		_, err := ups.fileService.CreateFolder(profile.Location)
		if err != nil {
			ups.logService.Error("Failed to create profile folder", err)
			promise <- nil
			return
		}

		// Fire will create event
		joiners := make([]chan error, 0)
		ups.onWillCreateProfile.Fire(&WillCreateProfileEvent{
			Profile: profile,
			Join: func(p chan error) {
				joiners = append(joiners, p)
			},
		})

		// Wait for all joiners
		for _, joiner := range joiners {
			<-joiner
		}

		// Handle empty window association
		if workspace != nil {
			if workspaceStr, ok := workspace.(string); ok {
				isTransient := profile.IsTransient != nil && *profile.IsTransient
				ups.updateEmptyWindowAssociation(workspaceStr, profile, isTransient)
			}
		}

		// Update profiles
		ups.updateProfiles([]*IUserDataProfile{profile}, []*IUserDataProfile{}, []*IUserDataProfile{})

		promise <- profile
	}()

	return <-promise, nil
}

// getWorkspace extracts workspace information from workspace identifier
func (ups *UserDataProfilesService) getWorkspace(workspaceIdentifier workspacecommon.IAnyWorkspaceIdentifier) interface{} {
	// This is a simplified implementation - in the real VS Code this would be more complex
	// For now, just return the identifier as-is
	return workspaceIdentifier
}

// updateEmptyWindowAssociation updates the association between empty windows and profiles
func (ups *UserDataProfilesService) updateEmptyWindowAssociation(windowID string, profile *IUserDataProfile, transient bool) {
	ups.mutex.Lock()
	defer ups.mutex.Unlock()

	if transient {
		if profile != nil {
			ups.transientProfilesObject.EmptyWindows[windowID] = profile
		} else {
			delete(ups.transientProfilesObject.EmptyWindows, windowID)
		}
	} else {
		// Remove from transient associations
		delete(ups.transientProfilesObject.EmptyWindows, windowID)

		profilesObj := ups.getProfilesObject()
		if profile != nil {
			profilesObj.EmptyWindows[windowID] = profile
		} else {
			delete(profilesObj.EmptyWindows, windowID)
		}
	}
}

// updateProfiles updates the profiles and fires change events
func (ups *UserDataProfilesService) updateProfiles(added, removed, updated []*IUserDataProfile) {
	ups.mutex.Lock()
	defer ups.mutex.Unlock()

	// Get all current profiles
	allProfiles := make([]*IUserDataProfile, 0)
	allProfiles = append(allProfiles, ups.Profiles()...)
	allProfiles = append(allProfiles, added...)

	// Remove profiles that are in the removed list
	filteredProfiles := make([]*IUserDataProfile, 0)
	for _, profile := range allProfiles {
		shouldRemove := false
		for _, removedProfile := range removed {
			if profile.ID == removedProfile.ID {
				shouldRemove = true
				break
			}
		}
		if !shouldRemove {
			filteredProfiles = append(filteredProfiles, profile)
		}
	}

	// Update profiles that are in the updated list
	for i, profile := range filteredProfiles {
		for _, updatedProfile := range updated {
			if profile.ID == updatedProfile.ID {
				filteredProfiles[i] = updatedProfile
				break
			}
		}
	}

	// Separate transient and persistent profiles
	persistentProfiles := make([]*IUserDataProfile, 0)
	transientProfiles := make([]*IUserDataProfile, 0)

	for _, profile := range filteredProfiles {
		if profile.IsTransient != nil && *profile.IsTransient {
			transientProfiles = append(transientProfiles, profile)
		} else {
			persistentProfiles = append(persistentProfiles, profile)
		}
	}

	// Update the profiles objects
	if ups.profilesObject == nil {
		ups.profilesObject = &UserDataProfilesObject{
			Profiles:     persistentProfiles,
			EmptyWindows: make(map[string]*IUserDataProfile),
		}
	} else {
		ups.profilesObject.Profiles = persistentProfiles
	}

	ups.transientProfilesObject.Profiles = transientProfiles

	// Update stored profiles
	ups.updateStoredProfiles(filteredProfiles)

	// Fire change event
	ups.triggerProfilesChanges(added, removed, updated)
}

// updateStoredProfiles updates the stored profiles
func (ups *UserDataProfilesService) updateStoredProfiles(profiles []*IUserDataProfile) {
	storedProfiles := make([]*StoredUserDataProfile, 0)
	workspaces := make(basecommon.IStringDictionary[string])
	emptyWindows := make(basecommon.IStringDictionary[string])

	for _, profile := range profiles {
		if profile.IsTransient != nil && *profile.IsTransient {
			continue
		}
		if !profile.IsDefault {
			storedProfiles = append(storedProfiles, &StoredUserDataProfile{
				Location:        profile.Location,
				Name:            profile.Name,
				Icon:            profile.Icon,
				UseDefaultFlags: profile.UseDefaultFlags,
			})
		}
		if profile.Workspaces != nil {
			for _, workspace := range profile.Workspaces {
				workspaces[workspace.ToString()] = profile.ID
			}
		}
	}

	// Add empty window associations
	profilesObj := ups.getProfilesObject()
	for windowID, profile := range profilesObj.EmptyWindows {
		emptyWindows[windowID] = profile.ID
	}

	ups.saveStoredProfileAssociations(&StoredProfileAssociations{
		Workspaces:   workspaces,
		EmptyWindows: emptyWindows,
	})
	ups.saveStoredProfiles(storedProfiles)
	ups.profilesObject = nil
}

// triggerProfilesChanges fires the profiles change event
func (ups *UserDataProfilesService) triggerProfilesChanges(added, removed, updated []*IUserDataProfile) {
	ups.onDidChangeProfiles.Fire(&DidChangeProfilesEvent{
		Added:   added,
		Removed: removed,
		Updated: updated,
		All:     ups.Profiles(),
	})
}

// UpdateProfile updates an existing profile
func (ups *UserDataProfilesService) UpdateProfile(profile *IUserDataProfile, options *IUserDataProfileUpdateOptions) (*IUserDataProfile, error) {
	ups.mutex.Lock()
	defer ups.mutex.Unlock()

	profilesToUpdate := make([]*IUserDataProfile, 0)

	for _, existing := range ups.Profiles() {
		var profileToUpdate *IUserDataProfile

		if profile.ID == existing.ID {
			if !existing.IsDefault {
				name := existing.Name
				if options.Name != nil {
					name = *options.Name
				}

				icon := existing.Icon
				if options.Icon != nil {
					icon = options.Icon
				}

				transient := existing.IsTransient
				if options.Transient != nil {
					transient = options.Transient
				}

				useDefaultFlags := existing.UseDefaultFlags
				if options.UseDefaultFlags != nil {
					useDefaultFlags = options.UseDefaultFlags
				}

				workspaces := existing.Workspaces
				if options.Workspaces != nil {
					workspaces = options.Workspaces
				}

				profileToUpdate = ToUserDataProfile(existing.ID, name, existing.Location, ups.profilesCacheHome, &IUserDataProfileOptions{
					Icon:            icon,
					Transient:       transient,
					UseDefaultFlags: useDefaultFlags,
					Workspaces:      workspaces,
				}, ups.DefaultProfile())
			} else if options.Workspaces != nil {
				profileToUpdate = existing
				profileToUpdate.Workspaces = options.Workspaces
			}
		} else if options.Workspaces != nil {
			// Remove workspaces from other profiles
			filteredWorkspaces := make([]*basecommon.URI, 0)
			if existing.Workspaces != nil {
				for _, w1 := range existing.Workspaces {
					shouldKeep := true
					for _, w2 := range options.Workspaces {
						if ups.uriIdentityService.ExtUri().IsEqual(w1, w2) {
							shouldKeep = false
							break
						}
					}
					if shouldKeep {
						filteredWorkspaces = append(filteredWorkspaces, w1)
					}
				}
			}

			if len(existing.Workspaces) != len(filteredWorkspaces) {
				profileToUpdate = existing
				profileToUpdate.Workspaces = filteredWorkspaces
			}
		}

		if profileToUpdate != nil {
			profilesToUpdate = append(profilesToUpdate, profileToUpdate)
		}
	}

	if len(profilesToUpdate) == 0 {
		if profile.IsDefault {
			return nil, fmt.Errorf("cannot update default profile")
		}
		return nil, fmt.Errorf("profile '%s' does not exist", profile.Name)
	}

	ups.updateProfiles([]*IUserDataProfile{}, []*IUserDataProfile{}, profilesToUpdate)

	// Find and return the updated profile
	for _, p := range ups.Profiles() {
		if p.ID == profile.ID {
			return p, nil
		}
	}

	return nil, fmt.Errorf("profile '%s' was not updated", profile.Name)
}

// RemoveProfile removes a profile
func (ups *UserDataProfilesService) RemoveProfile(profileToRemove *IUserDataProfile) error {
	if profileToRemove.IsDefault {
		return fmt.Errorf("cannot remove default profile")
	}

	var profile *IUserDataProfile
	for _, p := range ups.Profiles() {
		if p.ID == profileToRemove.ID {
			profile = p
			break
		}
	}

	if profile == nil {
		return fmt.Errorf("profile '%s' does not exist", profileToRemove.Name)
	}

	// Fire will remove event
	joiners := make([]chan error, 0)
	ups.onWillRemoveProfile.Fire(&WillRemoveProfileEvent{
		Profile: profile,
		Join: func(p chan error) {
			joiners = append(joiners, p)
		},
	})

	// Wait for all joiners
	for _, joiner := range joiners {
		<-joiner
	}

	ups.updateProfiles([]*IUserDataProfile{}, []*IUserDataProfile{profile}, []*IUserDataProfile{})

	// Clean up cache directory
	go func() {
		err := ups.fileService.Delete(profile.CacheHome, &filescommon.IFileDeleteOptions{Recursive: true})
		if err != nil {
			// Check if it's a file not found error
			if filescommon.ToFileOperationResult(err) != filescommon.FileOperationResultFileNotFound {
				ups.logService.Error("Failed to delete profile cache", err)
			}
		}
	}()

	return nil
}

// SetProfileForWorkspace sets the profile for a workspace
func (ups *UserDataProfilesService) SetProfileForWorkspace(workspaceIdentifier workspacecommon.IAnyWorkspaceIdentifier, profileToSet *IUserDataProfile) error {
	var profile *IUserDataProfile
	for _, p := range ups.Profiles() {
		if p.ID == profileToSet.ID {
			profile = p
			break
		}
	}

	if profile == nil {
		return fmt.Errorf("profile '%s' does not exist", profileToSet.Name)
	}

	workspace := ups.getWorkspace(workspaceIdentifier)
	if workspaceURI, ok := workspace.(*basecommon.URI); ok {
		workspaces := make([]*basecommon.URI, 0)
		if profile.Workspaces != nil {
			workspaces = append(workspaces, profile.Workspaces...)
		}

		// Check if workspace is already associated
		alreadyAssociated := false
		for _, w := range workspaces {
			if ups.uriIdentityService.ExtUri().IsEqual(w, workspaceURI) {
				alreadyAssociated = true
				break
			}
		}

		if !alreadyAssociated {
			workspaces = append(workspaces, workspaceURI)
			_, err := ups.UpdateProfile(profile, &IUserDataProfileUpdateOptions{Workspaces: workspaces})
			return err
		}
	} else if workspaceStr, ok := workspace.(string); ok {
		ups.updateEmptyWindowAssociation(workspaceStr, profile, false)
		ups.updateStoredProfiles(ups.Profiles())
	}

	return nil
}

// ResetWorkspaces resets all workspace associations
func (ups *UserDataProfilesService) ResetWorkspaces() error {
	ups.mutex.Lock()
	defer ups.mutex.Unlock()

	ups.transientProfilesObject.EmptyWindows = make(map[string]*IUserDataProfile)
	ups.getProfilesObject().EmptyWindows = make(map[string]*IUserDataProfile)

	for _, profile := range ups.Profiles() {
		profile.Workspaces = nil
	}

	ups.updateProfiles([]*IUserDataProfile{}, []*IUserDataProfile{}, ups.Profiles())
	ups.onDidResetWorkspaces.Fire(nil)

	return nil
}

// CleanUp cleans up unused profile directories
func (ups *UserDataProfilesService) CleanUp() error {
	exists, err := ups.fileService.Exists(ups.profilesHome)
	if err != nil || !exists {
		return err
	}

	stat, err := ups.fileService.Resolve(ups.profilesHome, &filescommon.IResolveFileOptions{ResolveTo: []*basecommon.URI{}})
	if err != nil {
		return err
	}

	if stat.Children != nil {
		for _, child := range stat.Children {
			if child.IsDirectory {
				// Check if this directory is used by any profile
				isUsed := false
				for _, profile := range ups.Profiles() {
					if ups.uriIdentityService.ExtUri().IsEqual(profile.Location, child.Resource) {
						isUsed = true
						break
					}
				}

				if !isUsed {
					err := ups.fileService.Delete(child.Resource, &filescommon.IFileDeleteOptions{Recursive: true})
					if err != nil {
						ups.logService.Error("Failed to delete unused profile directory", child.Resource, err)
					}
				}
			}
		}
	}

	return nil
}

// CleanUpTransientProfiles removes unassociated transient profiles
func (ups *UserDataProfilesService) CleanUpTransientProfiles() error {
	unassociatedProfiles := make([]*IUserDataProfile, 0)

	for _, profile := range ups.transientProfilesObject.Profiles {
		if !ups.isProfileAssociatedToWorkspace(profile) {
			unassociatedProfiles = append(unassociatedProfiles, profile)
		}
	}

	for _, profile := range unassociatedProfiles {
		err := ups.RemoveProfile(profile)
		if err != nil {
			ups.logService.Error("Failed to remove unassociated transient profile", profile.Name, err)
		}
	}

	return nil
}

// isProfileAssociatedToWorkspace checks if a profile is associated with any workspace
func (ups *UserDataProfilesService) isProfileAssociatedToWorkspace(profile *IUserDataProfile) bool {
	if profile.Workspaces != nil && len(profile.Workspaces) > 0 {
		return true
	}

	// Check empty window associations
	profilesObj := ups.getProfilesObject()
	for _, windowProfile := range profilesObj.EmptyWindows {
		if ups.uriIdentityService.ExtUri().IsEqual(windowProfile.Location, profile.Location) {
			return true
		}
	}

	for _, windowProfile := range ups.transientProfilesObject.EmptyWindows {
		if ups.uriIdentityService.ExtUri().IsEqual(windowProfile.Location, profile.Location) {
			return true
		}
	}

	return false
}

// InMemoryUserDataProfilesService is a simple in-memory implementation
type InMemoryUserDataProfilesService struct {
	*UserDataProfilesService
	storedProfiles            []*StoredUserDataProfile
	storedProfileAssociations *StoredProfileAssociations
}

// NewInMemoryUserDataProfilesService creates a new in-memory service
func NewInMemoryUserDataProfilesService(
	environmentService environmentcommon.IEnvironmentService,
	fileService filescommon.IFileService,
	uriIdentityService uriidentitycommon.IUriIdentityService,
	logService logcommon.ILogService,
) *InMemoryUserDataProfilesService {
	base := NewUserDataProfilesService(environmentService, fileService, uriIdentityService, logService)
	return &InMemoryUserDataProfilesService{
		UserDataProfilesService:   base,
		storedProfiles:            make([]*StoredUserDataProfile, 0),
		storedProfileAssociations: &StoredProfileAssociations{},
	}
}

// Override the storage methods for in-memory implementation
func (imups *InMemoryUserDataProfilesService) getStoredProfiles() []*StoredUserDataProfile {
	return imups.storedProfiles
}

func (imups *InMemoryUserDataProfilesService) saveStoredProfiles(profiles []*StoredUserDataProfile) {
	imups.storedProfiles = profiles
}

func (imups *InMemoryUserDataProfilesService) getStoredProfileAssociations() *StoredProfileAssociations {
	return imups.storedProfileAssociations
}

func (imups *InMemoryUserDataProfilesService) saveStoredProfileAssociations(associations *StoredProfileAssociations) {
	imups.storedProfileAssociations = associations
}

// Service identifier for dependency injection
var IUserDataProfilesServiceID = instantiationcommon.CreateDecorator[IUserDataProfilesService]("userDataProfilesService")
