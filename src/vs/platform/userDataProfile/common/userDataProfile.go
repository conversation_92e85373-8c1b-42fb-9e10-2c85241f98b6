/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	basecommon "src/vs/base/common"
	workspacecommon "src/vs/platform/workspace/common"
)

// ProfileResourceType represents the type of profile resource
type ProfileResourceType string

const (
	ProfileResourceTypeSettings    ProfileResourceType = "settings"
	ProfileResourceTypeKeybindings ProfileResourceType = "keybindings"
	ProfileResourceTypeSnippets    ProfileResourceType = "snippets"
	ProfileResourceTypePrompts     ProfileResourceType = "prompts"
	ProfileResourceTypeTasks       ProfileResourceType = "tasks"
	ProfileResourceTypeExtensions  ProfileResourceType = "extensions"
	ProfileResourceTypeGlobalState ProfileResourceType = "globalState"
	ProfileResourceTypeMcp         ProfileResourceType = "mcp"
)

// UseDefaultProfileFlags indicates whether to use the default profile or not
type UseDefaultProfileFlags map[ProfileResourceType]bool

// ProfileResourceTypeFlags is an alias for UseDefaultProfileFlags
type ProfileResourceTypeFlags = UseDefaultProfileFlags

// IUserDataProfile represents a user data profile
type IUserDataProfile struct {
	ID                  string                  `json:"id"`
	IsDefault           bool                    `json:"isDefault"`
	Name                string                  `json:"name"`
	Icon                *string                 `json:"icon,omitempty"`
	Location            *basecommon.URI         `json:"location"`
	GlobalStorageHome   *basecommon.URI         `json:"globalStorageHome"`
	SettingsResource    *basecommon.URI         `json:"settingsResource"`
	KeybindingsResource *basecommon.URI         `json:"keybindingsResource"`
	TasksResource       *basecommon.URI         `json:"tasksResource"`
	SnippetsHome        *basecommon.URI         `json:"snippetsHome"`
	PromptsHome         *basecommon.URI         `json:"promptsHome"`
	ExtensionsResource  *basecommon.URI         `json:"extensionsResource"`
	McpResource         *basecommon.URI         `json:"mcpResource"`
	CacheHome           *basecommon.URI         `json:"cacheHome"`
	UseDefaultFlags     *UseDefaultProfileFlags `json:"useDefaultFlags,omitempty"`
	IsTransient         *bool                   `json:"isTransient,omitempty"`
	Workspaces          []*basecommon.URI       `json:"workspaces,omitempty"`
}

// IsUserDataProfile checks if the given object is a valid IUserDataProfile
func IsUserDataProfile(thing interface{}) bool {
	if thing == nil {
		return false
	}

	profile, ok := thing.(*IUserDataProfile)
	if !ok {
		return false
	}

	return profile != nil &&
		profile.ID != "" &&
		profile.Name != "" &&
		profile.Location != nil &&
		profile.GlobalStorageHome != nil &&
		profile.SettingsResource != nil &&
		profile.KeybindingsResource != nil &&
		profile.TasksResource != nil &&
		profile.SnippetsHome != nil &&
		profile.PromptsHome != nil &&
		profile.ExtensionsResource != nil &&
		profile.McpResource != nil
}

// DidChangeProfilesEvent represents the event when profiles change
type DidChangeProfilesEvent struct {
	Added   []*IUserDataProfile `json:"added"`
	Removed []*IUserDataProfile `json:"removed"`
	Updated []*IUserDataProfile `json:"updated"`
	All     []*IUserDataProfile `json:"all"`
}

// WillCreateProfileEvent represents the event before creating a profile
type WillCreateProfileEvent struct {
	Profile *IUserDataProfile
	Join    func(promise chan error)
}

// WillRemoveProfileEvent represents the event before removing a profile
type WillRemoveProfileEvent struct {
	Profile *IUserDataProfile
	Join    func(promise chan error)
}

// IUserDataProfileOptions represents options for creating a user data profile
type IUserDataProfileOptions struct {
	Icon            *string                 `json:"icon,omitempty"`
	UseDefaultFlags *UseDefaultProfileFlags `json:"useDefaultFlags,omitempty"`
	Transient       *bool                   `json:"transient,omitempty"`
	Workspaces      []*basecommon.URI       `json:"workspaces,omitempty"`
}

// IUserDataProfileUpdateOptions represents options for updating a user data profile
type IUserDataProfileUpdateOptions struct {
	Name            *string                 `json:"name,omitempty"`
	Icon            *string                 `json:"icon,omitempty"`
	UseDefaultFlags *UseDefaultProfileFlags `json:"useDefaultFlags,omitempty"`
	Transient       *bool                   `json:"transient,omitempty"`
	Workspaces      []*basecommon.URI       `json:"workspaces,omitempty"`
}

// IUserDataProfilesService represents the user data profiles service interface
type IUserDataProfilesService interface {
	// Service brand for type safety
	ServiceBrand() interface{}

	// Properties
	ProfilesHome() *basecommon.URI
	DefaultProfile() *IUserDataProfile
	Profiles() []*IUserDataProfile

	// Events
	OnDidChangeProfiles() basecommon.Event[*DidChangeProfilesEvent]
	OnDidResetWorkspaces() basecommon.Event[interface{}]

	// Methods
	CreateNamedProfile(name string, options *IUserDataProfileOptions, workspaceIdentifier workspacecommon.IAnyWorkspaceIdentifier) (*IUserDataProfile, error)
	CreateTransientProfile(workspaceIdentifier workspacecommon.IAnyWorkspaceIdentifier) (*IUserDataProfile, error)
	CreateProfile(id string, name string, options *IUserDataProfileOptions, workspaceIdentifier workspacecommon.IAnyWorkspaceIdentifier) (*IUserDataProfile, error)
	UpdateProfile(profile *IUserDataProfile, options *IUserDataProfileUpdateOptions) (*IUserDataProfile, error)
	RemoveProfile(profile *IUserDataProfile) error

	SetProfileForWorkspace(workspaceIdentifier workspacecommon.IAnyWorkspaceIdentifier, profile *IUserDataProfile) error
	ResetWorkspaces() error

	CleanUp() error
	CleanUpTransientProfiles() error
}

// ReviveProfile revives a profile from a DTO with the given scheme
func ReviveProfile(profile map[string]interface{}, scheme string) *IUserDataProfile {
	// Helper function to revive URI with scheme
	reviveURI := func(uriData interface{}) *basecommon.URI {
		if uriData == nil {
			return nil
		}
		if uriMap, ok := uriData.(map[string]interface{}); ok {
			uri := basecommon.From(basecommon.UriComponents{
				Scheme:    getString(uriMap, "scheme"),
				Authority: getString(uriMap, "authority"),
				Path:      getString(uriMap, "path"),
				Query:     getString(uriMap, "query"),
				Fragment:  getString(uriMap, "fragment"),
			})
			return uri.With(basecommon.UriComponents{Scheme: scheme})
		}
		return nil
	}

	// Helper function to revive URI array
	reviveURIArray := func(uriArrayData interface{}) []*basecommon.URI {
		if uriArrayData == nil {
			return nil
		}
		if uriArray, ok := uriArrayData.([]interface{}); ok {
			result := make([]*basecommon.URI, len(uriArray))
			for i, uriData := range uriArray {
				result[i] = reviveURI(uriData)
			}
			return result
		}
		return nil
	}

	return &IUserDataProfile{
		ID:                  getString(profile, "id"),
		IsDefault:           getBool(profile, "isDefault"),
		Name:                getString(profile, "name"),
		Icon:                getStringPtr(profile, "icon"),
		Location:            reviveURI(profile["location"]),
		GlobalStorageHome:   reviveURI(profile["globalStorageHome"]),
		SettingsResource:    reviveURI(profile["settingsResource"]),
		KeybindingsResource: reviveURI(profile["keybindingsResource"]),
		TasksResource:       reviveURI(profile["tasksResource"]),
		SnippetsHome:        reviveURI(profile["snippetsHome"]),
		PromptsHome:         reviveURI(profile["promptsHome"]),
		ExtensionsResource:  reviveURI(profile["extensionsResource"]),
		McpResource:         reviveURI(profile["mcpResource"]),
		CacheHome:           reviveURI(profile["cacheHome"]),
		UseDefaultFlags:     getUseDefaultFlags(profile, "useDefaultFlags"),
		IsTransient:         getBoolPtr(profile, "isTransient"),
		Workspaces:          reviveURIArray(profile["workspaces"]),
	}
}

// Helper functions for type conversion
func getString(m map[string]interface{}, key string) string {
	if val, ok := m[key]; ok {
		if str, ok := val.(string); ok {
			return str
		}
	}
	return ""
}

func getBool(m map[string]interface{}, key string) bool {
	if val, ok := m[key]; ok {
		if b, ok := val.(bool); ok {
			return b
		}
	}
	return false
}

func getStringPtr(m map[string]interface{}, key string) *string {
	if val, ok := m[key]; ok {
		if str, ok := val.(string); ok {
			return &str
		}
	}
	return nil
}

func getBoolPtr(m map[string]interface{}, key string) *bool {
	if val, ok := m[key]; ok {
		if b, ok := val.(bool); ok {
			return &b
		}
	}
	return nil
}

func getUseDefaultFlags(m map[string]interface{}, key string) *UseDefaultProfileFlags {
	if val, ok := m[key]; ok {
		if flagsMap, ok := val.(map[string]interface{}); ok {
			flags := make(UseDefaultProfileFlags)
			for k, v := range flagsMap {
				if b, ok := v.(bool); ok {
					flags[ProfileResourceType(k)] = b
				}
			}
			return &flags
		}
	}
	return nil
}

// ToUserDataProfile creates a user data profile from the given parameters
func ToUserDataProfile(id, name string, location, profilesCacheHome *basecommon.URI, options *IUserDataProfileOptions, defaultProfile *IUserDataProfile) *IUserDataProfile {
	profile := &IUserDataProfile{
		ID:        id,
		Name:      name,
		Location:  location,
		IsDefault: false,
		CacheHome: basecommon.JoinPath(profilesCacheHome, id),
	}

	if options != nil {
		profile.Icon = options.Icon
		profile.UseDefaultFlags = options.UseDefaultFlags
		profile.IsTransient = options.Transient
		profile.Workspaces = options.Workspaces
	}

	// Set resource paths based on default flags
	if defaultProfile != nil && options != nil && options.UseDefaultFlags != nil {
		if (*options.UseDefaultFlags)[ProfileResourceTypeGlobalState] {
			profile.GlobalStorageHome = defaultProfile.GlobalStorageHome
		} else {
			profile.GlobalStorageHome = basecommon.JoinPath(location, "globalStorage")
		}

		if (*options.UseDefaultFlags)[ProfileResourceTypeSettings] {
			profile.SettingsResource = defaultProfile.SettingsResource
		} else {
			profile.SettingsResource = basecommon.JoinPath(location, "settings.json")
		}

		if (*options.UseDefaultFlags)[ProfileResourceTypeKeybindings] {
			profile.KeybindingsResource = defaultProfile.KeybindingsResource
		} else {
			profile.KeybindingsResource = basecommon.JoinPath(location, "keybindings.json")
		}

		if (*options.UseDefaultFlags)[ProfileResourceTypeTasks] {
			profile.TasksResource = defaultProfile.TasksResource
		} else {
			profile.TasksResource = basecommon.JoinPath(location, "tasks.json")
		}

		if (*options.UseDefaultFlags)[ProfileResourceTypeSnippets] {
			profile.SnippetsHome = defaultProfile.SnippetsHome
		} else {
			profile.SnippetsHome = basecommon.JoinPath(location, "snippets")
		}

		if (*options.UseDefaultFlags)[ProfileResourceTypePrompts] {
			profile.PromptsHome = defaultProfile.PromptsHome
		} else {
			profile.PromptsHome = basecommon.JoinPath(location, "prompts")
		}

		if (*options.UseDefaultFlags)[ProfileResourceTypeExtensions] {
			profile.ExtensionsResource = defaultProfile.ExtensionsResource
		} else {
			profile.ExtensionsResource = basecommon.JoinPath(location, "extensions.json")
		}

		if (*options.UseDefaultFlags)[ProfileResourceTypeMcp] {
			profile.McpResource = defaultProfile.McpResource
		} else {
			profile.McpResource = basecommon.JoinPath(location, "mcp.json")
		}
	} else {
		// Default paths when no default profile or flags
		profile.GlobalStorageHome = basecommon.JoinPath(location, "globalStorage")
		profile.SettingsResource = basecommon.JoinPath(location, "settings.json")
		profile.KeybindingsResource = basecommon.JoinPath(location, "keybindings.json")
		profile.TasksResource = basecommon.JoinPath(location, "tasks.json")
		profile.SnippetsHome = basecommon.JoinPath(location, "snippets")
		profile.PromptsHome = basecommon.JoinPath(location, "prompts")
		profile.ExtensionsResource = basecommon.JoinPath(location, "extensions.json")
		profile.McpResource = basecommon.JoinPath(location, "mcp.json")
	}

	return profile
}

// UserDataProfilesObject represents the profiles object
type UserDataProfilesObject struct {
	Profiles     []*IUserDataProfile          `json:"profiles"`
	EmptyWindows map[string]*IUserDataProfile `json:"emptyWindows"`
}

// StoredUserDataProfile represents a stored user data profile
type StoredUserDataProfile struct {
	Name            string                  `json:"name"`
	Location        *basecommon.URI         `json:"location"`
	Icon            *string                 `json:"icon,omitempty"`
	UseDefaultFlags *UseDefaultProfileFlags `json:"useDefaultFlags,omitempty"`
}

// StoredProfileAssociations represents stored profile associations
type StoredProfileAssociations struct {
	Workspaces   basecommon.IStringDictionary[string] `json:"workspaces,omitempty"`
	EmptyWindows basecommon.IStringDictionary[string] `json:"emptyWindows,omitempty"`
}
