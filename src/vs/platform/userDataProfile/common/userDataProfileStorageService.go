/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"fmt"
	"sync"

	basecommon "src/vs/base/common"
	storagecommon "src/vs/base/parts/storage/common"
	instantiationcommon "src/vs/platform/instantiation/common"
	ipccommon "src/vs/platform/ipc/common"
	logcommon "src/vs/platform/log/common"
	platformstoragecommon "src/vs/platform/storage/common"
)

// IProfileStorageValueChanges represents profile storage value changes
type IProfileStorageValueChanges struct {
	Profile *IUserDataProfile                         `json:"profile"`
	Changes []*storagecommon.IStorageValueChangeEvent `json:"changes"`
}

// IProfileStorageChanges represents profile storage changes
type IProfileStorageChanges struct {
	TargetChanges []*IUserDataProfile            `json:"targetChanges"`
	ValueChanges  []*IProfileStorageValueChanges `json:"valueChanges"`
}

// IStorageValue represents a storage value with its target
type IStorageValue struct {
	Value  *string                     `json:"value"`
	Target storagecommon.StorageTarget `json:"target"`
}

// IUserDataProfileStorageService represents the user data profile storage service interface
type IUserDataProfileStorageService interface {
	// Service brand for type safety
	ServiceBrand() interface{}

	// OnDidChange emitted whenever data is updated or deleted in a profile storage
	// or target of a profile storage entry changes
	OnDidChange() basecommon.Event[*IProfileStorageChanges]

	// ReadStorageData returns the requested profile storage data
	ReadStorageData(profile *IUserDataProfile) (map[string]*IStorageValue, error)

	// UpdateStorageData updates the given profile storage data in the profile storage
	UpdateStorageData(profile *IUserDataProfile, data map[string]*string, target storagecommon.StorageTarget) error

	// WithProfileScopedStorageService calls a function with a storage service scoped to given profile
	WithProfileScopedStorageService(profile *IUserDataProfile, fn func(storageService platformstoragecommon.IStorageService) (interface{}, error)) (interface{}, error)
}

// AbstractUserDataProfileStorageService provides a base implementation of IUserDataProfileStorageService
type AbstractUserDataProfileStorageService struct {
	*basecommon.DisposableStore

	// Properties
	storageService     platformstoragecommon.IStorageService
	storageServicesMap *basecommon.DisposableMap[string]
	mutex              sync.RWMutex
}

// NewAbstractUserDataProfileStorageService creates a new abstract user data profile storage service
func NewAbstractUserDataProfileStorageService(
	persistStorages bool,
	storageService platformstoragecommon.IStorageService,
) *AbstractUserDataProfileStorageService {
	service := &AbstractUserDataProfileStorageService{
		DisposableStore: basecommon.NewDisposableStore(),
		storageService:  storageService,
	}

	if persistStorages {
		service.storageServicesMap = basecommon.NewDisposableMap[string]()
		service.Register(service.storageServicesMap)
	}

	return service
}

// ServiceBrand implements the service brand
func (s *AbstractUserDataProfileStorageService) ServiceBrand() interface{} {
	return "userDataProfileStorageService"
}

// OnDidChange returns the event for profile storage changes (abstract method)
func (s *AbstractUserDataProfileStorageService) OnDidChange() basecommon.Event[*IProfileStorageChanges] {
	panic("OnDidChange must be implemented by subclass")
}

// ReadStorageData reads storage data for a profile
func (s *AbstractUserDataProfileStorageService) ReadStorageData(profile *IUserDataProfile) (map[string]*IStorageValue, error) {
	return s.WithProfileScopedStorageService(profile, func(storageService platformstoragecommon.IStorageService) (interface{}, error) {
		return s.getItems(storageService), nil
	})
}

// UpdateStorageData updates storage data for a profile
func (s *AbstractUserDataProfileStorageService) UpdateStorageData(profile *IUserDataProfile, data map[string]*string, target storagecommon.StorageTarget) error {
	_, err := s.WithProfileScopedStorageService(profile, func(storageService platformstoragecommon.IStorageService) (interface{}, error) {
		s.writeItems(storageService, data, target)
		return nil, nil
	})
	return err
}

// WithProfileScopedStorageService calls a function with a storage service scoped to the given profile
func (s *AbstractUserDataProfileStorageService) WithProfileScopedStorageService(profile *IUserDataProfile, fn func(storageService platformstoragecommon.IStorageService) (interface{}, error)) (interface{}, error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// Check if the main storage service has scope for this profile
	if s.storageService.HasScope(storagecommon.StorageScopeProfile) {
		return fn(s.storageService)
	}

	// Get or create a scoped storage service
	var storageService *StorageService
	if s.storageServicesMap != nil {
		if disposable, exists := s.storageServicesMap.Get(profile.ID); exists {
			storageService = disposable.(*StorageService)
		}
	}

	if storageService == nil {
		// Create storage database for the profile
		database, err := s.createStorageDatabase(profile)
		if err != nil {
			return nil, err
		}

		storageService = NewStorageService(database)
		if s.storageServicesMap != nil {
			s.storageServicesMap.Set(profile.ID, storageService)
		}

		// Initialize the storage service
		err = storageService.Initialize()
		if err != nil {
			if s.storageServicesMap != nil {
				if _, exists := s.storageServicesMap.Get(profile.ID); exists {
					s.storageServicesMap.Delete(profile.ID)
				}
			} else {
				storageService.Dispose()
			}
			return nil, err
		}
	}

	// Call the function with the storage service
	result, err := fn(storageService)

	// Flush the storage service
	if flushErr := storageService.Flush(platformstoragecommon.WillSaveStateReasonNone); flushErr != nil {
		// Log flush error but don't override the main error
		fmt.Printf("Failed to flush storage service: %v\n", flushErr)
	}

	// Dispose if not persisting
	shouldDispose := s.storageServicesMap == nil
	if !shouldDispose {
		if _, exists := s.storageServicesMap.Get(profile.ID); !exists {
			shouldDispose = true
		}
	}
	if shouldDispose {
		storageService.Dispose()
	}

	return result, err
}

// getItems retrieves all items from a storage service
func (s *AbstractUserDataProfileStorageService) getItems(storageService platformstoragecommon.IStorageService) map[string]*IStorageValue {
	result := make(map[string]*IStorageValue)

	// Helper function to populate items for a target
	populate := func(target storagecommon.StorageTarget) {
		keys := storageService.Keys(storagecommon.StorageScopeProfile, target)
		for _, key := range keys {
			value := storageService.GetOptional(key, storagecommon.StorageScopeProfile)
			result[key] = &IStorageValue{
				Value:  value,
				Target: target,
			}
		}
	}

	// Populate for both user and machine targets
	populate(storagecommon.StorageTargetUser)
	populate(storagecommon.StorageTargetMachine)

	return result
}

// writeItems writes items to a storage service
func (s *AbstractUserDataProfileStorageService) writeItems(storageService platformstoragecommon.IStorageService, items map[string]*string, target storagecommon.StorageTarget) {
	entries := make([]*platformstoragecommon.IStorageEntry, 0, len(items))
	for key, value := range items {
		var val interface{}
		if value != nil {
			val = *value
		}
		entries = append(entries, &platformstoragecommon.IStorageEntry{
			Key:    key,
			Value:  val,
			Scope:  storagecommon.StorageScopeProfile,
			Target: target,
		})
	}

	// Store all entries
	for _, entry := range entries {
		if entry.Value == nil {
			storageService.Remove(entry.Key, entry.Scope)
		} else {
			storageService.Store(entry.Key, entry.Value, entry.Scope, entry.Target)
		}
	}
}

// createStorageDatabase creates a storage database for the given profile (abstract method)
func (s *AbstractUserDataProfileStorageService) createStorageDatabase(profile *IUserDataProfile) (storagecommon.IStorageDatabase, error) {
	panic("createStorageDatabase must be implemented by subclass")
}

// RemoteUserDataProfileStorageService implements IUserDataProfileStorageService for remote scenarios
type RemoteUserDataProfileStorageService struct {
	*AbstractUserDataProfileStorageService

	// Events
	onDidChange *basecommon.Emitter[*IProfileStorageChanges]

	// Services
	remoteService           ipccommon.IRemoteService
	userDataProfilesService IUserDataProfilesService
	logService              logcommon.ILogService
	disposable              *basecommon.MutableDisposable
}

// NewRemoteUserDataProfileStorageService creates a new remote user data profile storage service
func NewRemoteUserDataProfileStorageService(
	persistStorages bool,
	remoteService ipccommon.IRemoteService,
	userDataProfilesService IUserDataProfilesService,
	storageService platformstoragecommon.IStorageService,
	logService logcommon.ILogService,
) *RemoteUserDataProfileStorageService {
	service := &RemoteUserDataProfileStorageService{
		AbstractUserDataProfileStorageService: NewAbstractUserDataProfileStorageService(persistStorages, storageService),
		remoteService:                         remoteService,
		userDataProfilesService:               userDataProfilesService,
		logService:                            logService,
		disposable:                            basecommon.NewMutableDisposable(),
	}

	// Initialize the change event emitter
	service.onDidChange = basecommon.NewEmitter[*IProfileStorageChanges]()

	service.Register(service.onDidChange)
	service.Register(service.disposable)

	return service
}

// OnDidChange returns the event for profile storage changes
func (s *RemoteUserDataProfileStorageService) OnDidChange() basecommon.Event[*IProfileStorageChanges] {
	return s.onDidChange.Event()
}

// startListening starts listening to remote profile storage changes
func (s *RemoteUserDataProfileStorageService) startListening() {
	channel := s.remoteService.GetChannel("profileStorageListener")
	listener := channel.Listen("onDidChange", nil)

	s.disposable.Set(basecommon.ToDisposable(func() {
		// Stop listening
	}))

	// Handle events
	go func() {
		for event := range listener {
			if changes, ok := event.(*IProfileStorageChanges); ok {
				s.logService.Trace("profile storage changes", changes)

				// Revive profiles in the changes
				revivedChanges := &IProfileStorageChanges{
					TargetChanges: make([]*IUserDataProfile, len(changes.TargetChanges)),
					ValueChanges:  make([]*IProfileStorageValueChanges, len(changes.ValueChanges)),
				}

				for i, profile := range changes.TargetChanges {
					revivedChanges.TargetChanges[i] = ReviveProfile(profile, s.userDataProfilesService.ProfilesHome().Scheme)
				}

				for i, valueChange := range changes.ValueChanges {
					revivedChanges.ValueChanges[i] = &IProfileStorageValueChanges{
						Profile: ReviveProfile(valueChange.Profile, s.userDataProfilesService.ProfilesHome().Scheme),
						Changes: valueChange.Changes,
					}
				}

				s.onDidChange.Fire(revivedChanges)
			}
		}
	}()
}

// stopListening stops listening to remote profile storage changes
func (s *RemoteUserDataProfileStorageService) stopListening() {
	s.disposable.Set(nil)
}

// createStorageDatabase creates a storage database for the given profile
func (s *RemoteUserDataProfileStorageService) createStorageDatabase(profile *IUserDataProfile) (storagecommon.IStorageDatabase, error) {
	storageChannel := s.remoteService.GetChannel("storage")

	if platformstoragecommon.IsProfileUsingDefaultStorage(profile) {
		return platformstoragecommon.NewApplicationStorageDatabaseClient(storageChannel), nil
	} else {
		return platformstoragecommon.NewProfileStorageDatabaseClient(storageChannel, profile), nil
	}
}

// StorageService is a concrete implementation of storage service for profiles
type StorageService struct {
	*platformstoragecommon.AbstractStorageService

	profileStorage         storagecommon.IStorage
	profileStorageDatabase storagecommon.IStorageDatabase
	mutex                  sync.RWMutex
}

// NewStorageService creates a new storage service
func NewStorageService(profileStorageDatabase storagecommon.IStorageDatabase) *StorageService {
	service := &StorageService{
		AbstractStorageService: platformstoragecommon.NewAbstractStorageService(),
		profileStorageDatabase: profileStorageDatabase,
	}

	return service
}

// Initialize initializes the storage service
func (s *StorageService) Initialize() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// Create profile storage
	profileStorage, err := storagecommon.NewStorage(
		s.profileStorageDatabase,
		storagecommon.StorageScopeProfile,
		storagecommon.StorageTargetUser,
	)
	if err != nil {
		return err
	}

	// Register storage change listener
	s.Register(basecommon.ToDisposable(func() {
		profileStorage.OnDidChangeValue().Dispose()
	}))

	// Register storage disposal
	s.Register(basecommon.ToDisposable(func() {
		profileStorage.Close()
		profileStorage.Dispose()
		if disposable, ok := s.profileStorageDatabase.(basecommon.IDisposable); ok {
			disposable.Dispose()
		}
	}))

	s.profileStorage = profileStorage

	// Initialize the storage
	return s.profileStorage.Init()
}

// GetStorage returns the storage for the given scope
func (s *StorageService) GetStorage(scope storagecommon.StorageScope) storagecommon.IStorage {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if scope == storagecommon.StorageScopeProfile {
		return s.profileStorage
	}
	return nil
}

// GetLogDetails returns log details for the storage service
func (s *StorageService) GetLogDetails(scope storagecommon.StorageScope) string {
	return ""
}

// ShouldFlushWhenIdle returns whether the storage should flush when idle
func (s *StorageService) ShouldFlushWhenIdle() bool {
	return false
}

// HasScope returns whether the service has the given scope
func (s *StorageService) HasScope(scope storagecommon.StorageScope) bool {
	return scope == storagecommon.StorageScopeProfile
}

// Service identifier for dependency injection
var IUserDataProfileStorageServiceID = instantiationcommon.CreateDecorator[IUserDataProfileStorageService]("userDataProfileStorageService")
