/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	basecommon "src/vs/base/common"
	ipccommon "src/vs/base/parts/ipc/common"
	storagecommon "src/vs/base/parts/storage/common"
	workspacecommon "src/vs/platform/workspace/common"
)

// Key represents a storage key
type Key = string

// Value represents a storage value
type Value = string

// Item represents a key-value pair
type Item = [2]string // [Key, Value]

// IBaseSerializableStorageRequest represents a base serializable storage request
type IBaseSerializableStorageRequest struct {
	// Profile to correlate storage. Only used when no workspace is provided.
	// Can be nil to denote application scope.
	Profile interface{} `json:"profile,omitempty"`

	// Workspace to correlate storage. Can be nil to denote application or profile scope.
	Workspace workspacecommon.IAnyWorkspaceIdentifier `json:"workspace,omitempty"`

	// Additional payload for the request to perform.
	Payload interface{} `json:"payload,omitempty"`
}

// ISerializableUpdateRequest represents a serializable update request
type ISerializableUpdateRequest struct {
	IBaseSerializableStorageRequest
	Insert []Item `json:"insert,omitempty"`
	Delete []Key  `json:"delete,omitempty"`
}

// ISerializableItemsChangeEvent represents a serializable items change event
type ISerializableItemsChangeEvent struct {
	Changed []Item `json:"changed,omitempty"`
	Deleted []Key  `json:"deleted,omitempty"`
}

// BaseStorageDatabaseClient is an abstract base class for storage database clients
type BaseStorageDatabaseClient struct {
	*basecommon.Disposable
	channel   ipccommon.IChannel
	profile   interface{}
	workspace workspacecommon.IAnyWorkspaceIdentifier
}

// NewBaseStorageDatabaseClient creates a new base storage database client
func NewBaseStorageDatabaseClient(
	channel ipccommon.IChannel,
	profile interface{},
	workspace workspacecommon.IAnyWorkspaceIdentifier,
) *BaseStorageDatabaseClient {
	return &BaseStorageDatabaseClient{
		Disposable: basecommon.NewDisposable(),
		channel:    channel,
		profile:    profile,
		workspace:  workspace,
	}
}

// GetItems retrieves all items from storage
func (client *BaseStorageDatabaseClient) GetItems() (map[string]string, error) {
	request := &IBaseSerializableStorageRequest{
		Profile:   client.profile,
		Workspace: client.workspace,
	}

	items, err := client.channel.Call("getItems", request)
	if err != nil {
		return nil, err
	}

	// Convert items array to map
	result := make(map[string]string)
	if itemsArray, ok := items.([]Item); ok {
		for _, item := range itemsArray {
			result[item[0]] = item[1]
		}
	}

	return result, nil
}

// UpdateItems updates items in storage
func (client *BaseStorageDatabaseClient) UpdateItems(request *storagecommon.IUpdateRequest) error {
	serializableRequest := &ISerializableUpdateRequest{
		IBaseSerializableStorageRequest: IBaseSerializableStorageRequest{
			Profile:   client.profile,
			Workspace: client.workspace,
		},
	}

	if request.Insert != nil {
		serializableRequest.Insert = make([]Item, 0, len(request.Insert))
		for key, value := range request.Insert {
			serializableRequest.Insert = append(serializableRequest.Insert, Item{key, value})
		}
	}

	if request.Delete != nil {
		serializableRequest.Delete = make([]Key, 0, len(request.Delete))
		for key := range request.Delete {
			serializableRequest.Delete = append(serializableRequest.Delete, key)
		}
	}

	_, err := client.channel.Call("updateItems", serializableRequest)
	return err
}

// Optimize optimizes the storage database
func (client *BaseStorageDatabaseClient) Optimize() error {
	request := &IBaseSerializableStorageRequest{
		Profile:   client.profile,
		Workspace: client.workspace,
	}

	_, err := client.channel.Call("optimize", request)
	return err
}

// Close closes the storage database client (abstract method)
func (client *BaseStorageDatabaseClient) Close() error {
	panic("Close must be implemented by subclass")
}

// CheckIntegrity checks the integrity of the storage database
func (client *BaseStorageDatabaseClient) CheckIntegrity(full bool) (string, error) {
	request := &IBaseSerializableStorageRequest{
		Profile:   client.profile,
		Workspace: client.workspace,
		Payload:   full,
	}

	result, err := client.channel.Call("checkIntegrity", request)
	if err != nil {
		return "", err
	}

	if message, ok := result.(string); ok {
		return message, nil
	}

	return "", nil
}

// BaseProfileAwareStorageDatabaseClient extends BaseStorageDatabaseClient with profile awareness
type BaseProfileAwareStorageDatabaseClient struct {
	*BaseStorageDatabaseClient
	*basecommon.DisposableStore
	onDidChangeItemsExternal *basecommon.Emitter[*storagecommon.IStorageItemsChangeEvent]
}

// NewBaseProfileAwareStorageDatabaseClient creates a new profile-aware storage database client
func NewBaseProfileAwareStorageDatabaseClient(
	channel ipccommon.IChannel,
	profile interface{},
) *BaseProfileAwareStorageDatabaseClient {
	client := &BaseProfileAwareStorageDatabaseClient{
		BaseStorageDatabaseClient: NewBaseStorageDatabaseClient(channel, profile, nil),
		DisposableStore:           basecommon.NewDisposableStore(),
		onDidChangeItemsExternal:  basecommon.NewEmitter[*storagecommon.IStorageItemsChangeEvent](),
	}

	client.registerListeners()
	return client
}

// OnDidChangeItemsExternal returns the event for external item changes
func (client *BaseProfileAwareStorageDatabaseClient) OnDidChangeItemsExternal() basecommon.Event[*storagecommon.IStorageItemsChangeEvent] {
	return client.onDidChangeItemsExternal.Event()
}

// registerListeners registers event listeners
func (client *BaseProfileAwareStorageDatabaseClient) registerListeners() {
	// Register listener for storage changes
	request := map[string]interface{}{
		"profile": client.profile,
	}

	listener := client.channel.Listen("onDidChangeStorage", request)
	client.Register(basecommon.ToDisposable(func() {
		// Dispose listener
	}))

	// Handle storage change events
	go func() {
		for event := range listener {
			if changeEvent, ok := event.(*ISerializableItemsChangeEvent); ok {
				client.onDidChangeStorage(changeEvent)
			}
		}
	}()
}

// onDidChangeStorage handles storage change events
func (client *BaseProfileAwareStorageDatabaseClient) onDidChangeStorage(e *ISerializableItemsChangeEvent) {
	if len(e.Changed) > 0 || len(e.Deleted) > 0 {
		var changed map[string]interface{}
		var deleted []string

		if len(e.Changed) > 0 {
			changed = make(map[string]interface{})
			for _, item := range e.Changed {
				changed[item[0]] = item[1]
			}
		}

		if len(e.Deleted) > 0 {
			deleted = make([]string, len(e.Deleted))
			copy(deleted, e.Deleted)
		}

		client.onDidChangeItemsExternal.Fire(&storagecommon.IStorageItemsChangeEvent{
			Changed: changed,
			Deleted: deleted,
		})
	}
}

// ApplicationStorageDatabaseClient represents an application storage database client
type ApplicationStorageDatabaseClient struct {
	*BaseProfileAwareStorageDatabaseClient
}

// NewApplicationStorageDatabaseClient creates a new application storage database client
func NewApplicationStorageDatabaseClient(channel ipccommon.IChannel) *ApplicationStorageDatabaseClient {
	return &ApplicationStorageDatabaseClient{
		BaseProfileAwareStorageDatabaseClient: NewBaseProfileAwareStorageDatabaseClient(channel, nil),
	}
}

// Close closes the application storage database client
func (client *ApplicationStorageDatabaseClient) Close() error {
	// The application storage database is shared across all instances so
	// we do not close it from the window. However we dispose the
	// listener for external changes because we no longer interested in it.
	client.Dispose()
	return nil
}

// ProfileStorageDatabaseClient represents a profile storage database client
type ProfileStorageDatabaseClient struct {
	*BaseProfileAwareStorageDatabaseClient
}

// NewProfileStorageDatabaseClient creates a new profile storage database client
func NewProfileStorageDatabaseClient(channel ipccommon.IChannel, profile interface{}) *ProfileStorageDatabaseClient {
	return &ProfileStorageDatabaseClient{
		BaseProfileAwareStorageDatabaseClient: NewBaseProfileAwareStorageDatabaseClient(channel, profile),
	}
}

// Close closes the profile storage database client
func (client *ProfileStorageDatabaseClient) Close() error {
	// The profile storage database is shared across all instances of
	// the same profile so we do not close it from the window.
	// However we dispose the listener for external changes because
	// we no longer interested in it.
	client.Dispose()
	return nil
}

// WorkspaceStorageDatabaseClient represents a workspace storage database client
type WorkspaceStorageDatabaseClient struct {
	*BaseStorageDatabaseClient
}

// NewWorkspaceStorageDatabaseClient creates a new workspace storage database client
func NewWorkspaceStorageDatabaseClient(channel ipccommon.IChannel, workspace workspacecommon.IAnyWorkspaceIdentifier) *WorkspaceStorageDatabaseClient {
	return &WorkspaceStorageDatabaseClient{
		BaseStorageDatabaseClient: NewBaseStorageDatabaseClient(channel, nil, workspace),
	}
}

// OnDidChangeItemsExternal returns an empty event (unsupported for workspace storage)
func (client *WorkspaceStorageDatabaseClient) OnDidChangeItemsExternal() basecommon.Event[*storagecommon.IStorageItemsChangeEvent] {
	return basecommon.EventNone[*storagecommon.IStorageItemsChangeEvent]()
}

// Close closes the workspace storage database client
func (client *WorkspaceStorageDatabaseClient) Close() error {
	// The workspace storage database is only used in this instance
	// but we do not need to close it from here, the main process
	// can take care of that.
	client.Dispose()
	return nil
}

// StorageClient represents a storage client
type StorageClient struct {
	channel ipccommon.IChannel
}

// NewStorageClient creates a new storage client
func NewStorageClient(channel ipccommon.IChannel) *StorageClient {
	return &StorageClient{
		channel: channel,
	}
}

// IsUsed checks if a storage path is used
func (client *StorageClient) IsUsed(path string) (bool, error) {
	request := &ISerializableUpdateRequest{
		IBaseSerializableStorageRequest: IBaseSerializableStorageRequest{
			Payload:   path,
			Profile:   nil,
			Workspace: nil,
		},
	}

	result, err := client.channel.Call("isUsed", request)
	if err != nil {
		return false, err
	}

	if used, ok := result.(bool); ok {
		return used, nil
	}

	return false, nil
}
