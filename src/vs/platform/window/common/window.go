/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

// IColorScheme represents the color scheme information
type IColorScheme struct {
	Dark         bool `json:"dark"`
	HighContrast bool `json:"highContrast"`
}

// WindowMinimumSize defines minimum window dimensions
type WindowMinimumSize struct {
	Width                  int
	WidthWithVerticalPanel int
	Height                 int
}

// Default window minimum sizes
var DefaultWindowMinimumSize = WindowMinimumSize{
	Width:                  400,
	WidthWithVerticalPanel: 600,
	Height:                 270,
}

// IPoint represents a point with x and y coordinates
type IPoint struct {
	X int `json:"x"`
	Y int `json:"y"`
}

// IRectangle represents a rectangle with position and dimensions
type IRectangle struct {
	IPoint
	Width  int `json:"width"`
	Height int `json:"height"`
}

// MenuBarVisibility represents the visibility state of the menu bar
type MenuBarVisibility string

const (
	MenuBarVisibilityClassic MenuBarVisibility = "classic"
	MenuBarVisibilityVisible MenuBarVisibility = "visible"
	MenuBarVisibilityToggle  MenuBarVisibility = "toggle"
	MenuBarVisibilityHidden  MenuBarVisibility = "hidden"
	MenuBarVisibilityCompact MenuBarVisibility = "compact"
)

// TitlebarStyle represents the style of the title bar
type TitlebarStyle string

const (
	TitlebarStyleNative TitlebarStyle = "native"
	TitlebarStyleCustom TitlebarStyle = "custom"
)

// WindowControlsStyle represents the style of window controls
type WindowControlsStyle string

const (
	WindowControlsStyleNative WindowControlsStyle = "native"
	WindowControlsStyleCustom WindowControlsStyle = "custom"
	WindowControlsStyleHidden WindowControlsStyle = "hidden"
)

// MenuStyleConfiguration represents menu style configuration
type MenuStyleConfiguration string

const (
	MenuStyleCustom  MenuStyleConfiguration = "custom"
	MenuStyleNative  MenuStyleConfiguration = "native"
	MenuStyleInherit MenuStyleConfiguration = "inherit"
)

// CustomTitleBarVisibility represents custom title bar visibility
type CustomTitleBarVisibility string

const (
	CustomTitleBarVisibilityAuto     CustomTitleBarVisibility = "auto"
	CustomTitleBarVisibilityWindowed CustomTitleBarVisibility = "windowed"
	CustomTitleBarVisibilityNever    CustomTitleBarVisibility = "never"
)

// IDensitySettings represents density settings for UI elements
type IDensitySettings struct {
	EditorTabHeight string `json:"editorTabHeight"` // "default" | "compact"
}

// IWindowSettings represents window-related settings
type IWindowSettings struct {
	OpenFilesInNewWindow            string                 `json:"openFilesInNewWindow"`            // "on" | "off" | "default"
	OpenFoldersInNewWindow          string                 `json:"openFoldersInNewWindow"`          // "on" | "off" | "default"
	OpenWithoutArgumentsInNewWindow string                 `json:"openWithoutArgumentsInNewWindow"` // "on" | "off"
	RestoreWindows                  string                 `json:"restoreWindows"`                  // "preserve" | "all" | "folders" | "one" | "none"
	RestoreFullscreen               bool                   `json:"restoreFullscreen"`
	ZoomLevel                       float64                `json:"zoomLevel"`
	TitleBarStyle                   TitlebarStyle          `json:"titleBarStyle"`
	ControlsStyle                   WindowControlsStyle    `json:"controlsStyle"`
	MenuStyle                       MenuStyleConfiguration `json:"menuStyle"`
	AutoDetectHighContrast          bool                   `json:"autoDetectHighContrast"`
	AutoDetectColorScheme           bool                   `json:"autoDetectColorScheme"`
	MenuBarVisibility               MenuBarVisibility      `json:"menuBarVisibility"`
	NewWindowDimensions             string                 `json:"newWindowDimensions"` // "default" | "inherit" | "offset" | "maximized" | "fullscreen"
	NativeTabs                      bool                   `json:"nativeTabs"`
	NativeFullScreen                bool                   `json:"nativeFullScreen"`
	EnableMenuBarMnemonics          bool                   `json:"enableMenuBarMnemonics"`
	CloseWhenEmpty                  bool                   `json:"closeWhenEmpty"`
	ClickThroughInactive            bool                   `json:"clickThroughInactive"`
	NewWindowProfile                string                 `json:"newWindowProfile"`
	Density                         IDensitySettings       `json:"density"`
	Border                          string                 `json:"border"` // "off" | "default" | color
}

// IWindowsConfiguration represents windows configuration
type IWindowsConfiguration struct {
	Window IWindowSettings `json:"window"`
}

// DefaultWindowSize represents default window dimensions
type DefaultWindowSize struct {
	Width  int
	Height int
}

// Default window sizes
var (
	DefaultMainWindowSize = DefaultWindowSize{Width: 1200, Height: 800}
	DefaultAuxWindowSize  = DefaultWindowSize{Width: 1024, Height: 768}
)

// DefaultCustomTitlebarHeight represents the default height of custom titlebar
const DefaultCustomTitlebarHeight = 35

// ZoomLevelToZoomFactor converts zoom level to zoom factor
// According to Electron docs: scale := 1.2 ^ level
func ZoomLevelToZoomFactor(zoomLevel float64) float64 {
	// Using math.Pow equivalent: 1.2^zoomLevel
	result := 1.0
	base := 1.2
	level := zoomLevel

	if level == 0 {
		return 1.0
	}

	if level > 0 {
		for i := 0; i < int(level); i++ {
			result *= base
		}
		// Handle fractional part
		if level != float64(int(level)) {
			// Simplified fractional power calculation
			result *= 1.0 + (level-float64(int(level)))*(base-1.0)
		}
	} else {
		level = -level
		for i := 0; i < int(level); i++ {
			result /= base
		}
		// Handle fractional part
		if level != float64(int(level)) {
			// Simplified fractional power calculation
			result /= 1.0 + (level-float64(int(level)))*(base-1.0)
		}
	}

	return result
}
