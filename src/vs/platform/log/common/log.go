/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import "context"

// LogLevel represents the log level
type LogLevel int

const (
	LogLevelOff LogLevel = iota
	LogLevelTrace
	LogLevelDebug
	LogLevelInfo
	LogLevelWarn
	LogLevelError
)

// String returns the string representation of the log level
func (l LogLevel) String() string {
	switch l {
	case LogLevelOff:
		return "off"
	case LogLevelTrace:
		return "trace"
	case LogLevelDebug:
		return "debug"
	case LogLevelInfo:
		return "info"
	case LogLevelWarn:
		return "warn"
	case LogLevelError:
		return "error"
	default:
		return "unknown"
	}
}

// ILogger interface for logging
type ILogger interface {
	GetLevel() LogLevel
	SetLevel(level LogLevel)

	Trace(message string, args ...interface{})
	Debug(message string, args ...interface{})
	Info(message string, args ...interface{})
	Warn(message string, args ...interface{})
	Error(message string, args ...interface{})

	Flush()
	Dispose()
}

// ILogService interface for the log service
type ILogService interface {
	ILogger

	CreateLogger(resource interface{}, options ...interface{}) ILogger
}

// AbstractLogger provides base functionality for loggers
type AbstractLogger struct {
	level LogLevel
}

// NewAbstractLogger creates a new abstract logger
func NewAbstractLogger(level LogLevel) *AbstractLogger {
	return &AbstractLogger{
		level: level,
	}
}

// GetLevel returns the current log level
func (l *AbstractLogger) GetLevel() LogLevel {
	return l.level
}

// SetLevel sets the log level
func (l *AbstractLogger) SetLevel(level LogLevel) {
	l.level = level
}

// CheckLogLevel checks if a message should be logged at the given level
func (l *AbstractLogger) CheckLogLevel(level LogLevel) bool {
	return l.level != LogLevelOff && level >= l.level
}

// Trace logs a trace message
func (l *AbstractLogger) Trace(message string, args ...interface{}) {
	if l.CheckLogLevel(LogLevelTrace) {
		l.doLog(LogLevelTrace, message, args...)
	}
}

// Debug logs a debug message
func (l *AbstractLogger) Debug(message string, args ...interface{}) {
	if l.CheckLogLevel(LogLevelDebug) {
		l.doLog(LogLevelDebug, message, args...)
	}
}

// Info logs an info message
func (l *AbstractLogger) Info(message string, args ...interface{}) {
	if l.CheckLogLevel(LogLevelInfo) {
		l.doLog(LogLevelInfo, message, args...)
	}
}

// Warn logs a warning message
func (l *AbstractLogger) Warn(message string, args ...interface{}) {
	if l.CheckLogLevel(LogLevelWarn) {
		l.doLog(LogLevelWarn, message, args...)
	}
}

// Error logs an error message
func (l *AbstractLogger) Error(message string, args ...interface{}) {
	if l.CheckLogLevel(LogLevelError) {
		l.doLog(LogLevelError, message, args...)
	}
}

// doLog is the actual logging implementation - to be overridden by subclasses
func (l *AbstractLogger) doLog(level LogLevel, message string, args ...interface{}) {
	// Default implementation does nothing
}

// Flush flushes any pending log messages
func (l *AbstractLogger) Flush() {
	// Default implementation does nothing
}

// Dispose disposes of the logger
func (l *AbstractLogger) Dispose() {
	// Default implementation does nothing
}

// ConsoleLogger implements a console logger
type ConsoleLogger struct {
	*AbstractLogger
}

// NewConsoleLogger creates a new console logger
func NewConsoleLogger(level LogLevel) *ConsoleLogger {
	return &ConsoleLogger{
		AbstractLogger: NewAbstractLogger(level),
	}
}

// doLog implements console logging
func (l *ConsoleLogger) doLog(level LogLevel, message string, args ...interface{}) {
	// In a real implementation, this would write to console/stdout
	// For now, this is a placeholder
}

// NullLogger implements a null logger that does nothing
type NullLogger struct {
	*AbstractLogger
}

// NewNullLogger creates a new null logger
func NewNullLogger() *NullLogger {
	return &NullLogger{
		AbstractLogger: NewAbstractLogger(LogLevelOff),
	}
}

// MultiplexLogger implements a logger that forwards to multiple loggers
type MultiplexLogger struct {
	*AbstractLogger
	loggers []ILogger
}

// NewMultiplexLogger creates a new multiplex logger
func NewMultiplexLogger(loggers []ILogger) *MultiplexLogger {
	level := LogLevelOff
	if len(loggers) > 0 {
		level = loggers[0].GetLevel()
	}

	return &MultiplexLogger{
		AbstractLogger: NewAbstractLogger(level),
		loggers:        loggers,
	}
}

// SetLevel sets the level on all child loggers
func (l *MultiplexLogger) SetLevel(level LogLevel) {
	l.AbstractLogger.SetLevel(level)
	for _, logger := range l.loggers {
		logger.SetLevel(level)
	}
}

// doLog forwards to all child loggers
func (l *MultiplexLogger) doLog(level LogLevel, message string, args ...interface{}) {
	for _, logger := range l.loggers {
		switch level {
		case LogLevelTrace:
			logger.Trace(message, args...)
		case LogLevelDebug:
			logger.Debug(message, args...)
		case LogLevelInfo:
			logger.Info(message, args...)
		case LogLevelWarn:
			logger.Warn(message, args...)
		case LogLevelError:
			logger.Error(message, args...)
		}
	}
}

// Flush flushes all child loggers
func (l *MultiplexLogger) Flush() {
	for _, logger := range l.loggers {
		logger.Flush()
	}
}

// Dispose disposes all child loggers
func (l *MultiplexLogger) Dispose() {
	for _, logger := range l.loggers {
		logger.Dispose()
	}
}

// LogService implements the main log service
type LogService struct {
	*AbstractLogger
	primaryLogger ILogger
}

// NewLogService creates a new log service
func NewLogService(primaryLogger ILogger) *LogService {
	level := LogLevelInfo
	if primaryLogger != nil {
		level = primaryLogger.GetLevel()
	}

	return &LogService{
		AbstractLogger: NewAbstractLogger(level),
		primaryLogger:  primaryLogger,
	}
}

// CreateLogger creates a new logger for a resource
func (l *LogService) CreateLogger(resource interface{}, options ...interface{}) ILogger {
	// In a real implementation, this would create resource-specific loggers
	// For now, return the primary logger
	if l.primaryLogger != nil {
		return l.primaryLogger
	}
	return NewNullLogger()
}

// doLog forwards to the primary logger
func (l *LogService) doLog(level LogLevel, message string, args ...interface{}) {
	if l.primaryLogger != nil {
		switch level {
		case LogLevelTrace:
			l.primaryLogger.Trace(message, args...)
		case LogLevelDebug:
			l.primaryLogger.Debug(message, args...)
		case LogLevelInfo:
			l.primaryLogger.Info(message, args...)
		case LogLevelWarn:
			l.primaryLogger.Warn(message, args...)
		case LogLevelError:
			l.primaryLogger.Error(message, args...)
		}
	}
}

// Flush flushes the primary logger
func (l *LogService) Flush() {
	if l.primaryLogger != nil {
		l.primaryLogger.Flush()
	}
}

// Dispose disposes the primary logger
func (l *LogService) Dispose() {
	if l.primaryLogger != nil {
		l.primaryLogger.Dispose()
	}
}

// Global logger instance
var DefaultLogger ILogger = NewNullLogger()

// SetDefaultLogger sets the global default logger
func SetDefaultLogger(logger ILogger) {
	DefaultLogger = logger
}

// Convenience functions that use the default logger

// LogTrace logs a trace message using the default logger
func LogTrace(message string, args ...interface{}) {
	DefaultLogger.Trace(message, args...)
}

// LogDebug logs a debug message using the default logger
func LogDebug(message string, args ...interface{}) {
	DefaultLogger.Debug(message, args...)
}

// LogInfo logs an info message using the default logger
func LogInfo(message string, args ...interface{}) {
	DefaultLogger.Info(message, args...)
}

// LogWarn logs a warning message using the default logger
func LogWarn(message string, args ...interface{}) {
	DefaultLogger.Warn(message, args...)
}

// LogError logs an error message using the default logger
func LogError(message string, args ...interface{}) {
	DefaultLogger.Error(message, args...)
}

// LogService dependency injection helpers

// LogServiceContext provides context for log service dependency injection
type LogServiceContext struct {
	context.Context
	logService ILogService
}

// NewLogServiceContext creates a new context with log service
func NewLogServiceContext(ctx context.Context, logService ILogService) *LogServiceContext {
	return &LogServiceContext{
		Context:    ctx,
		logService: logService,
	}
}

// GetLogService retrieves the log service from context
func GetLogService(ctx context.Context) ILogService {
	if lsc, ok := ctx.(*LogServiceContext); ok {
		return lsc.logService
	}
	return NewLogService(DefaultLogger)
}

// NullLogService is a null implementation of ILogService
type NullLogService struct {
	*NullLogger
}

// NewNullLogService creates a new NullLogService
func NewNullLogService() *NullLogService {
	return &NullLogService{
		NullLogger: NewNullLogger(),
	}
}

// CreateLogger creates a new logger for a resource
func (l *NullLogService) CreateLogger(resource interface{}, options ...interface{}) ILogger {
	return NewNullLogger()
}
