package node

import (
	instantiationcommon "src/vs/platform/instantiation/common"
)

// IStateReadService is the interface for the state read service.
type IStateReadService interface {
	GetItem(key string, defaultValue interface{}) interface{}
}

// IStateService is the interface for the state service.
type IStateService interface {
	IStateReadService
	SetItem(key string, data interface{})
	SetItems(items []StateItem)
	RemoveItem(key string)
	Close() error
}

// StateItem represents a key-value pair in the state service.
type StateItem struct {
	Key  string
	Data interface{}
}

// IStateService is a decorator for the state service.
var IStateServiceID = instantiationcommon.CreateDecorator[IStateService]("stateService")
