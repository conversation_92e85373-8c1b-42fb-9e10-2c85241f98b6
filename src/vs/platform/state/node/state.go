/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	instantiationcommon "src/vs/platform/instantiation/common"
)

// IStateReadService provides read-only access to state storage
type IStateReadService interface {
	// Service brand for type safety
	ServiceBrand() interface{}

	// GetItem retrieves an item from state storage with a default value
	GetItem(key string, defaultValue interface{}) interface{}

	// GetItemOptional retrieves an item from state storage, returning nil if not found
	GetItemOptional(key string) interface{}
}

// IStateService provides read-write access to state storage
type IStateService interface {
	IStateReadService

	// SetItem stores an item in state storage
	SetItem(key string, data interface{}) error

	// SetItems stores multiple items in state storage
	SetItems(items []StateItem) error

	// RemoveItem removes an item from state storage
	RemoveItem(key string) error

	// Close closes the state service and persists any pending changes
	Close() error
}

// StateItem represents a key-value pair for state storage
type StateItem struct {
	Key  string      `json:"key"`
	Data interface{} `json:"data"`
}

// Service identifiers for dependency injection
var IStateReadServiceID = instantiationcommon.CreateDecorator[IStateReadService]("stateReadService")
var IStateServiceID = instantiationcommon.CreateDecorator[IStateService]("stateService")
