/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	ipccommon "src/vs/base/parts/ipc/common"
	instantiationcommon "src/vs/platform/instantiation/common"
)

// IRemoteService represents a remote service interface
type IRemoteService interface {
	// Service brand for type safety
	ServiceBrand() interface{}

	// GetChannel gets a channel by name
	GetChannel(channelName string) ipccommon.IChannel

	// RegisterChannel registers a server channel
	RegisterChannel(channelName string, channel ipccommon.IServerChannel[string])
}

// Service identifier for dependency injection
var IRemoteServiceID = instantiationcommon.CreateDecorator[IRemoteService]("remoteService")
