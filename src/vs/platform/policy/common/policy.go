package common

import "src/vs/base/common"

// IPolicyService is the interface for the policy service.
type IPolicyService interface {
	GetPolicyValue(name string) interface{}
	UpdatePolicyDefinitions(definitions map[string]*PolicyDefinition) (interface{}, error)
	OnDidChange() common.Event[[]string]
}

// PolicyDefinition represents a policy definition.
type PolicyDefinition struct {
	Type           string
	PreviewFeature *bool
	DefaultValue   interface{}
}
