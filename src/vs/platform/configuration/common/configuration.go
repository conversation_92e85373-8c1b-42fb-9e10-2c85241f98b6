/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"fmt"
	"reflect"
	"strings"

	basecommon "src/vs/base/common"
	instantiationcommon "src/vs/platform/instantiation/common"
)

// ConfigurationServiceToken service identifier for IConfigurationService
var ConfigurationServiceToken = instantiationcommon.CreateDecorator[IConfigurationService]("configurationService")

// IConfigurationOverrides represents configuration overrides
type IConfigurationOverrides struct {
	OverrideIdentifier *string         `json:"overrideIdentifier,omitempty"`
	Resource           *basecommon.URI `json:"resource,omitempty"`
}

// IsConfigurationOverrides checks if the provided value is a valid IConfigurationOverrides
func IsConfigurationOverrides(thing interface{}) bool {
	if thing == nil {
		return false
	}

	v := reflect.ValueOf(thing)
	if v.Kind() != reflect.Struct && v.Kind() != reflect.Ptr {
		return false
	}

	// Use type assertion for more precise checking
	if overrides, ok := thing.(*IConfigurationOverrides); ok {
		return overrides != nil
	}

	if overrides, ok := thing.(IConfigurationOverrides); ok {
		// Additional validation could be added here
		_ = overrides
		return true
	}

	return false
}

// IConfigurationUpdateOverrides represents configuration update overrides
type IConfigurationUpdateOverrides struct {
	OverrideIdentifiers []string        `json:"overrideIdentifiers,omitempty"`
	Resource            *basecommon.URI `json:"resource,omitempty"`
}

// IsConfigurationUpdateOverrides checks if the provided value is a valid IConfigurationUpdateOverrides
func IsConfigurationUpdateOverrides(thing interface{}) bool {
	if thing == nil {
		return false
	}

	v := reflect.ValueOf(thing)
	if v.Kind() != reflect.Struct && v.Kind() != reflect.Ptr {
		return false
	}

	// Use type assertion for more precise checking
	if overrides, ok := thing.(*IConfigurationUpdateOverrides); ok {
		return overrides != nil
	}

	if overrides, ok := thing.(IConfigurationUpdateOverrides); ok {
		// Additional validation could be added here
		_ = overrides
		return true
	}

	return false
}

// ConfigurationTarget enum represents different configuration targets
type ConfigurationTarget int

const (
	ConfigurationTargetApplication ConfigurationTarget = iota + 1
	ConfigurationTargetUser
	ConfigurationTargetUserLocal
	ConfigurationTargetUserRemote
	ConfigurationTargetWorkspace
	ConfigurationTargetWorkspaceFolder
	ConfigurationTargetDefault
	ConfigurationTargetMemory
)

// ConfigurationTargetToString converts ConfigurationTarget to string
func ConfigurationTargetToString(configurationTarget ConfigurationTarget) string {
	switch configurationTarget {
	case ConfigurationTargetApplication:
		return "APPLICATION"
	case ConfigurationTargetUser:
		return "USER"
	case ConfigurationTargetUserLocal:
		return "USER_LOCAL"
	case ConfigurationTargetUserRemote:
		return "USER_REMOTE"
	case ConfigurationTargetWorkspace:
		return "WORKSPACE"
	case ConfigurationTargetWorkspaceFolder:
		return "WORKSPACE_FOLDER"
	case ConfigurationTargetDefault:
		return "DEFAULT"
	case ConfigurationTargetMemory:
		return "MEMORY"
	default:
		return "UNKNOWN"
	}
}

// IConfigurationChange represents a configuration change
type IConfigurationChange struct {
	Keys      []string         `json:"keys"`
	Overrides [][2]interface{} `json:"overrides"` // [string, string[]][]
}

// IConfigurationChangeEvent represents a configuration change event
type IConfigurationChangeEvent interface {
	GetSource() ConfigurationTarget
	GetAffectedKeys() map[string]bool
	GetChange() *IConfigurationChange
	AffectsConfiguration(configuration string, overrides *IConfigurationOverrides) bool
}

// IInspectValue represents an inspected configuration value
type IInspectValue[T any] struct {
	Value     *T                         `json:"value,omitempty"`
	Override  *T                         `json:"override,omitempty"`
	Overrides []IInspectValueOverride[T] `json:"overrides,omitempty"`
}

// IInspectValueOverride represents an override in inspect value
type IInspectValueOverride[T any] struct {
	Identifiers []string `json:"identifiers"`
	Value       T        `json:"value"`
}

// IConfigurationValue represents a configuration value with all targets
type IConfigurationValue[T any] struct {
	DefaultValue         *T `json:"defaultValue,omitempty"`
	ApplicationValue     *T `json:"applicationValue,omitempty"`
	UserValue            *T `json:"userValue,omitempty"`
	UserLocalValue       *T `json:"userLocalValue,omitempty"`
	UserRemoteValue      *T `json:"userRemoteValue,omitempty"`
	WorkspaceValue       *T `json:"workspaceValue,omitempty"`
	WorkspaceFolderValue *T `json:"workspaceFolderValue,omitempty"`
	MemoryValue          *T `json:"memoryValue,omitempty"`
	PolicyValue          *T `json:"policyValue,omitempty"`
	Value                *T `json:"value,omitempty"`

	Default         *IInspectValue[T] `json:"default,omitempty"`
	Application     *IInspectValue[T] `json:"application,omitempty"`
	User            *IInspectValue[T] `json:"user,omitempty"`
	UserLocal       *IInspectValue[T] `json:"userLocal,omitempty"`
	UserRemote      *IInspectValue[T] `json:"userRemote,omitempty"`
	Workspace       *IInspectValue[T] `json:"workspace,omitempty"`
	WorkspaceFolder *IInspectValue[T] `json:"workspaceFolder,omitempty"`
	Memory          *IInspectValue[T] `json:"memory,omitempty"`
	Policy          *IPolicyValue[T]  `json:"policy,omitempty"`

	OverrideIdentifiers []string `json:"overrideIdentifiers,omitempty"`
}

// IPolicyValue represents a policy value
type IPolicyValue[T any] struct {
	Value *T `json:"value,omitempty"`
}

// GetConfigValueInTarget gets configuration value in a specific target
func GetConfigValueInTarget[T any](configValue *IConfigurationValue[T], scope ConfigurationTarget) *T {
	switch scope {
	case ConfigurationTargetApplication:
		return configValue.ApplicationValue
	case ConfigurationTargetUser:
		return configValue.UserValue
	case ConfigurationTargetUserLocal:
		return configValue.UserLocalValue
	case ConfigurationTargetUserRemote:
		return configValue.UserRemoteValue
	case ConfigurationTargetWorkspace:
		return configValue.WorkspaceValue
	case ConfigurationTargetWorkspaceFolder:
		return configValue.WorkspaceFolderValue
	case ConfigurationTargetDefault:
		return configValue.DefaultValue
	case ConfigurationTargetMemory:
		return configValue.MemoryValue
	default:
		basecommon.AssertNever(scope)
		return nil
	}
}

// IsConfigured checks if a configuration value is configured in any target
func IsConfigured[T any](configValue *IConfigurationValue[T]) bool {
	return configValue.ApplicationValue != nil ||
		configValue.UserValue != nil ||
		configValue.UserLocalValue != nil ||
		configValue.UserRemoteValue != nil ||
		configValue.WorkspaceValue != nil ||
		configValue.WorkspaceFolderValue != nil
}

// IConfigurationUpdateOptions represents options for configuration updates
type IConfigurationUpdateOptions struct {
	// If true, do not notify the error to user by showing the message box. Default is false.
	DonotNotifyError *bool `json:"donotNotifyError,omitempty"`
	// How to handle dirty file when updating the configuration.
	HandleDirtyFile *string `json:"handleDirtyFile,omitempty"` // 'save' | 'revert'
}

// IConfigurationService represents the configuration service interface
type IConfigurationService interface {
	instantiationcommon.BrandedService

	// Event handlers
	OnDidChangeConfiguration() basecommon.Event[IConfigurationChangeEvent]

	// Get configuration data
	GetConfigurationData() *IConfigurationData

	// GetValue fetches the value of the section for the given overrides
	GetValue(section ...interface{}) interface{}

	// UpdateValue updates a configuration value
	UpdateValue(key string, value interface{}, args ...interface{}) error

	// Inspect inspects a configuration key
	Inspect(key string, overrides *IConfigurationOverrides) *IConfigurationValue[interface{}]

	// ReloadConfiguration reloads configuration
	ReloadConfiguration(target interface{}) error

	// Keys returns all configuration keys
	Keys() *IConfigurationKeys
}

// IConfigurationKeys represents configuration keys
type IConfigurationKeys struct {
	Default         []string `json:"default"`
	User            []string `json:"user"`
	Workspace       []string `json:"workspace"`
	WorkspaceFolder []string `json:"workspaceFolder"`
	Memory          []string `json:"memory,omitempty"`
}

// IConfigurationModel represents a configuration model
type IConfigurationModel struct {
	Contents  interface{}                               `json:"contents"`
	Keys      []string                                  `json:"keys"`
	Overrides []IOverrides                              `json:"overrides"`
	Raw       basecommon.IStringDictionary[interface{}] `json:"raw,omitempty"`
}

// IOverrides represents configuration overrides
type IOverrides struct {
	Keys        []string    `json:"keys"`
	Contents    interface{} `json:"contents"`
	Identifiers []string    `json:"identifiers"`
}

// IConfigurationData represents configuration data
type IConfigurationData struct {
	Defaults    *IConfigurationModel `json:"defaults"`
	Policy      *IConfigurationModel `json:"policy"`
	Application *IConfigurationModel `json:"application"`
	UserLocal   *IConfigurationModel `json:"userLocal"`
	UserRemote  *IConfigurationModel `json:"userRemote"`
	Workspace   *IConfigurationModel `json:"workspace"`
	Folders     [][]interface{}      `json:"folders"` // [UriComponents, IConfigurationModel][]
}

// IConfigurationCompareResult represents configuration comparison result
type IConfigurationCompareResult struct {
	Added     []string         `json:"added"`
	Removed   []string         `json:"removed"`
	Updated   []string         `json:"updated"`
	Overrides [][2]interface{} `json:"overrides"` // [string, string[]][]
}

// ToValuesTree converts properties to a values tree
func ToValuesTree(properties map[string]interface{}, conflictReporter func(string)) interface{} {
	root := make(map[string]interface{})

	for key, value := range properties {
		AddToValueTree(root, key, value, conflictReporter)
	}

	return root
}

// AddToValueTree adds a value to the settings tree
func AddToValueTree(settingsTreeRoot interface{}, key string, value interface{}, conflictReporter func(string)) {
	segments := strings.Split(key, ".")
	if len(segments) == 0 {
		return
	}

	last := segments[len(segments)-1]
	segments = segments[:len(segments)-1]

	// Convert settingsTreeRoot to map if it's not already
	curr, ok := settingsTreeRoot.(map[string]interface{})
	if !ok {
		return
	}

	for i, s := range segments {
		obj, exists := curr[s]
		if !exists {
			curr[s] = make(map[string]interface{})
			obj = curr[s]
		}

		switch objTyped := obj.(type) {
		case map[string]interface{}:
			curr = objTyped
		case nil:
			if conflictReporter != nil {
				conflictReporter(fmt.Sprintf("Ignoring %s as %s is null", key, strings.Join(segments[:i+1], ".")))
			}
			return
		default:
			if conflictReporter != nil {
				conflictReporter(fmt.Sprintf("Ignoring %s as %s is %v", key, strings.Join(segments[:i+1], "."), obj))
			}
			return
		}
	}

	if curr != nil {
		curr[last] = value
	}
}

// RemoveFromValueTree removes a value from the tree
func RemoveFromValueTree(valueTree interface{}, key string) {
	segments := strings.Split(key, ".")
	doRemoveFromValueTree(valueTree, segments)
}

func doRemoveFromValueTree(valueTree interface{}, segments []string) {
	if valueTree == nil || len(segments) == 0 {
		return
	}

	tree, ok := valueTree.(map[string]interface{})
	if !ok {
		return
	}

	first := segments[0]
	if len(segments) == 1 {
		// Reached last segment
		delete(tree, first)
		return
	}

	if value, exists := tree[first]; exists {
		if valueMap, ok := value.(map[string]interface{}); ok {
			doRemoveFromValueTree(valueMap, segments[1:])
			if len(valueMap) == 0 {
				delete(tree, first)
			}
		}
	}
}

// GetConfigurationValue gets a configuration value with a specific settings path
func GetConfigurationValue[T any](config interface{}, settingPath string, defaultValue ...T) interface{} {
	path := strings.Split(settingPath, ".")
	result := accessSetting(config, path)

	if result == nil && len(defaultValue) > 0 {
		return defaultValue[0]
	}

	return result
}

func accessSetting(config interface{}, path []string) interface{} {
	current := config
	for _, component := range path {
		if current == nil {
			return nil
		}

		switch currentTyped := current.(type) {
		case map[string]interface{}:
			current = currentTyped[component]
		default:
			return nil
		}
	}
	return current
}

// Merge merges two objects
func Merge(base interface{}, add interface{}, overwrite bool) {
	baseMap, ok1 := base.(map[string]interface{})
	addMap, ok2 := add.(map[string]interface{})

	if !ok1 || !ok2 {
		return
	}

	for key, value := range addMap {
		if key == "__proto__" {
			continue
		}

		if baseValue, exists := baseMap[key]; exists {
			if basecommon.IsObject(baseValue) && basecommon.IsObject(value) {
				Merge(baseValue, value, overwrite)
			} else if overwrite {
				baseMap[key] = value
			}
		} else {
			baseMap[key] = value
		}
	}
}

// GetLanguageTagSettingPlainKey converts language tag setting key to plain key
func GetLanguageTagSettingPlainKey(settingKey string) string {
	result := settingKey
	result = strings.TrimPrefix(result, "[")
	result = strings.TrimSuffix(result, "]")
	result = strings.ReplaceAll(result, "][", ", ")
	return result
}
