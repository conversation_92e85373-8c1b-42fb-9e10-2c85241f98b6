/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"fmt"
	"sync"

	basecommon "src/vs/base/common"
	filescommon "src/vs/platform/files/common"
	logcommon "src/vs/platform/log/common"
	policycommon "src/vs/platform/policy/common"
)

// ConfigurationService extends Disposable and implements IConfigurationService
type ConfigurationService struct {
	*basecommon.Disposable

	// Service brand for dependency injection
	serviceBrand interface{}

	// Configuration state
	configuration                *Configuration
	defaultConfiguration         *DefaultConfiguration
	policyConfiguration          IPolicyConfiguration
	userConfiguration            *UserSettings
	reloadConfigurationScheduler *basecommon.RunOnceScheduler

	// Event emitters
	onDidChangeConfigurationEmitter *basecommon.Emitter[IConfigurationChangeEvent]

	// Configuration editing
	configurationEditing *ConfigurationEditing

	// Dependencies
	settingsResource *basecommon.URI
	logService       logcommon.ILogService

	mu sync.RWMutex
}

// NewConfigurationService creates a new configuration service
func NewConfigurationService(
	settingsResource *basecommon.URI,
	fileService filescommon.IFileService,
	policyService policycommon.IPolicyService,
	logService logcommon.ILogService,
) *ConfigurationService {
	service := &ConfigurationService{
		Disposable:       basecommon.NewDisposable(),
		settingsResource: settingsResource,
		logService:       logService,
	}

	// Initialize configurations
	service.defaultConfiguration = service.Register(NewDefaultConfiguration(logService)).(*DefaultConfiguration)

	if _, ok := policyService.(*policycommon.NullPolicyService); ok {
		service.policyConfiguration = NewNullPolicyConfiguration()
	} else {
		service.policyConfiguration = service.Register(NewPolicyConfiguration(service.defaultConfiguration, policyService, logService)).(IPolicyConfiguration)
	}

	extUri := basecommon.GetExtUriIgnorePathCase()
	service.userConfiguration = service.Register(NewUserSettings(settingsResource, map[string]interface{}{}, extUri, fileService, logService)).(*UserSettings)

	// Initialize configuration
	service.configuration = NewConfiguration(
		service.defaultConfiguration.GetConfigurationModel(),
		service.policyConfiguration.GetConfigurationModel(),
		NewConfigurationModelEmpty(logService),
		NewConfigurationModelEmpty(logService),
		NewConfigurationModelEmpty(logService),
		NewConfigurationModelEmpty(logService),
		basecommon.NewResourceMap[*ConfigurationModel](),
		NewConfigurationModelEmpty(logService),
		basecommon.NewResourceMap[*ConfigurationModel](),
		logService,
	)

	// Initialize configuration editing
	service.configurationEditing = NewConfigurationEditing(settingsResource, fileService, service)

	// Initialize event emitters
	service.onDidChangeConfigurationEmitter = service.Register(basecommon.NewEmitter[IConfigurationChangeEvent]()).(*basecommon.Emitter[IConfigurationChangeEvent])

	// Initialize scheduler
	service.reloadConfigurationScheduler = service.Register(basecommon.NewRunOnceScheduler(func() {
		service.ReloadConfiguration()
	}, 50)).(*basecommon.RunOnceScheduler)

	// Register event listeners
	service.Register(service.defaultConfiguration.OnDidChangeConfiguration(func(args struct {
		Defaults   *ConfigurationModel
		Properties []string
	}) {
		service.onDidDefaultConfigurationChange(args.Defaults, args.Properties)
	}))

	service.Register(service.policyConfiguration.OnDidChangeConfiguration(func(model *ConfigurationModel) {
		service.onDidPolicyConfigurationChange(model)
	}))

	service.Register(service.userConfiguration.OnDidChange(func() {
		service.reloadConfigurationScheduler.Schedule()
	}))

	return service
}

// Initialize initializes the configuration service
func (cs *ConfigurationService) Initialize() error {
	// Load all configurations in parallel
	defaultModelChan := make(chan *ConfigurationModel, 1)
	policyModelChan := make(chan *ConfigurationModel, 1)
	userModelChan := make(chan *ConfigurationModel, 1)

	var wg sync.WaitGroup
	wg.Add(3)

	// Load default configuration
	go func() {
		defer wg.Done()
		model, err := cs.defaultConfiguration.Initialize()
		if err != nil {
			cs.logService.Error("Failed to initialize default configuration", err)
			defaultModelChan <- NewConfigurationModelEmpty(cs.logService)
		} else {
			defaultModelChan <- model
		}
	}()

	// Load policy configuration
	go func() {
		defer wg.Done()
		model, err := cs.policyConfiguration.Initialize()
		if err != nil {
			cs.logService.Error("Failed to initialize policy configuration", err)
			policyModelChan <- NewConfigurationModelEmpty(cs.logService)
		} else {
			policyModelChan <- model
		}
	}()

	// Load user configuration
	go func() {
		defer wg.Done()
		model, err := cs.userConfiguration.LoadConfiguration()
		if err != nil {
			cs.logService.Error("Failed to load user configuration", err)
			userModelChan <- NewConfigurationModelEmpty(cs.logService)
		} else {
			userModelChan <- model
		}
	}()

	wg.Wait()

	defaultModel := <-defaultModelChan
	policyModel := <-policyModelChan
	userModel := <-userModelChan

	cs.mu.Lock()
	cs.configuration = NewConfiguration(
		defaultModel,
		policyModel,
		NewConfigurationModelEmpty(cs.logService),
		userModel,
		NewConfigurationModelEmpty(cs.logService),
		NewConfigurationModelEmpty(cs.logService),
		basecommon.NewResourceMap[*ConfigurationModel](),
		NewConfigurationModelEmpty(cs.logService),
		basecommon.NewResourceMap[*ConfigurationModel](),
		cs.logService,
	)
	cs.mu.Unlock()

	return nil
}

// GetConfigurationData returns the configuration data
func (cs *ConfigurationService) GetConfigurationData() IConfigurationData {
	cs.mu.RLock()
	defer cs.mu.RUnlock()
	return cs.configuration.ToData()
}

// GetValue gets a configuration value
func (cs *ConfigurationService) GetValue(section string) interface{} {
	cs.mu.RLock()
	defer cs.mu.RUnlock()
	return cs.configuration.GetValue(section, &IConfigurationOverrides{}, nil)
}

// GetValueWithOverrides gets a configuration value with overrides
func (cs *ConfigurationService) GetValueWithOverrides(section string, overrides *IConfigurationOverrides) interface{} {
	if overrides == nil {
		overrides = &IConfigurationOverrides{}
	}

	cs.mu.RLock()
	defer cs.mu.RUnlock()
	return cs.configuration.GetValue(section, overrides, nil)
}

// IConfigurationUpdateOptions represents configuration update options
type IConfigurationUpdateOptions struct {
	DonotNotifyError bool `json:"donotNotifyError"`
}

// UpdateValue updates a configuration value
func (cs *ConfigurationService) UpdateValue(key string, value interface{}, args ...interface{}) error {
	var overrides *IConfigurationUpdateOverrides
	var target ConfigurationTarget
	var options *IConfigurationUpdateOptions

	// Parse arguments
	for _, arg := range args {
		switch v := arg.(type) {
		case *IConfigurationUpdateOverrides:
			overrides = v
		case *IConfigurationOverrides:
			// Convert to update overrides
			overrides = &IConfigurationUpdateOverrides{
				Resource: v.Resource,
				OverrideIdentifiers: func() []string {
					if v.OverrideIdentifier != nil && *v.OverrideIdentifier != "" {
						return []string{*v.OverrideIdentifier}
					}
					return nil
				}(),
			}
		case ConfigurationTarget:
			target = v
		case *IConfigurationUpdateOptions:
			options = v
		}
	}

	// Validate target
	if target != UserLocal && target != User {
		return fmt.Errorf("unable to write %s to target %d", key, target)
	}

	// Handle override identifiers
	if overrides != nil && overrides.OverrideIdentifiers != nil {
		identifiers := basecommon.Unique(overrides.OverrideIdentifiers)
		if len(identifiers) == 0 {
			overrides.OverrideIdentifiers = nil
		} else {
			overrides.OverrideIdentifiers = identifiers
		}
	}

	// Check for policy restrictions
	inspectOverrides := &IConfigurationOverrides{}
	if overrides != nil {
		inspectOverrides.Resource = overrides.Resource
		if overrides.OverrideIdentifiers != nil && len(overrides.OverrideIdentifiers) > 0 {
			id := overrides.OverrideIdentifiers[0]
			inspectOverrides.OverrideIdentifier = &id
		}
	}

	inspect := cs.Inspect(key, inspectOverrides)

	if inspect.PolicyValue != nil {
		return fmt.Errorf("unable to write %s because it is configured in system policy", key)
	}

	// Remove the setting if the value is same as default value
	if basecommon.Equals(value, inspect.DefaultValue) {
		value = nil
	}

	// Handle multiple override identifiers
	if overrides != nil && overrides.GetOverrideIdentifiers() != nil && len(overrides.GetOverrideIdentifiers()) > 1 {
		// Sort identifiers for consistent comparison
		identifiers := make([]string, len(overrides.GetOverrideIdentifiers()))
		copy(identifiers, overrides.GetOverrideIdentifiers())
		basecommon.Sort(identifiers, func(a, b string) int {
			if a < b {
				return -1
			} else if a > b {
				return 1
			}
			return 0
		})

		// Find existing overrides with same identifiers
		cs.mu.RLock()
		localUserConfig := cs.configuration.GetLocalUserConfiguration()
		cs.mu.RUnlock()

		for _, override := range localUserConfig.GetOverrides() {
			overrideIds := make([]string, len(override.GetIdentifiers()))
			copy(overrideIds, override.GetIdentifiers())
			basecommon.Sort(overrideIds, func(a, b string) int {
				if a < b {
					return -1
				} else if a > b {
					return 1
				}
				return 0
			})

			if basecommon.ArrayEquals(overrideIds, identifiers) {
				overrides.SetOverrideIdentifiers(override.GetIdentifiers())
				break
			}
		}
	}

	// Build path
	var path basecommon.JSONPath
	if overrides != nil && overrides.GetOverrideIdentifiers() != nil && len(overrides.GetOverrideIdentifiers()) > 0 {
		path = basecommon.JSONPath{KeyFromOverrideIdentifiers(overrides.GetOverrideIdentifiers()), key}
	} else {
		path = basecommon.JSONPath{key}
	}

	// Write configuration
	err := cs.configurationEditing.Write(path, value)
	if err != nil {
		return err
	}

	// Reload configuration
	return cs.ReloadConfiguration()
}

// Inspect inspects a configuration value
func (cs *ConfigurationService) Inspect(key string, overrides *IConfigurationOverrides) *IConfigurationValue[interface{}] {
	if overrides == nil {
		overrides = &IConfigurationOverrides{}
	}

	cs.mu.RLock()
	defer cs.mu.RUnlock()
	return cs.configuration.Inspect(key, overrides, nil)
}

// Keys returns configuration keys
func (cs *ConfigurationService) Keys() struct {
	Default         []string `json:"default"`
	User            []string `json:"user"`
	Workspace       []string `json:"workspace"`
	WorkspaceFolder []string `json:"workspaceFolder"`
} {
	cs.mu.RLock()
	defer cs.mu.RUnlock()
	return cs.configuration.Keys(nil)
}

// ReloadConfiguration reloads the configuration
func (cs *ConfigurationService) ReloadConfiguration() error {
	configurationModel, err := cs.userConfiguration.LoadConfiguration()
	if err != nil {
		return err
	}

	cs.onDidChangeUserConfiguration(configurationModel)
	return nil
}

// OnDidChangeConfiguration returns the configuration change event
func (cs *ConfigurationService) OnDidChangeConfiguration() basecommon.Event[IConfigurationChangeEvent] {
	return cs.onDidChangeConfigurationEmitter.Event()
}

// Private methods

func (cs *ConfigurationService) onDidChangeUserConfiguration(userConfigurationModel *ConfigurationModel) {
	cs.mu.Lock()
	previous := cs.configuration.ToData()
	change := cs.configuration.CompareAndUpdateLocalUserConfiguration(userConfigurationModel)
	cs.mu.Unlock()

	cs.trigger(change, previous, ConfigurationTargetUser)
}

func (cs *ConfigurationService) onDidDefaultConfigurationChange(defaultConfigurationModel *ConfigurationModel, properties []string) {
	cs.mu.Lock()
	previous := cs.configuration.ToData()
	change := cs.configuration.CompareAndUpdateDefaultConfiguration(defaultConfigurationModel, properties)
	cs.mu.Unlock()

	cs.trigger(change, previous, ConfigurationTargetDefault)
}

func (cs *ConfigurationService) onDidPolicyConfigurationChange(policyConfiguration *ConfigurationModel) {
	cs.mu.Lock()
	previous := cs.configuration.ToData()
	change := cs.configuration.CompareAndUpdatePolicyConfiguration(policyConfiguration)
	cs.mu.Unlock()

	cs.trigger(change, previous, ConfigurationTargetDefault)
}

func (cs *ConfigurationService) trigger(configurationChange IConfigurationChange, previous IConfigurationData, source ConfigurationTarget) {
	event := NewConfigurationChangeEvent(configurationChange, &ConfigurationChangeEventData{Data: previous}, cs.configuration, nil, cs.logService)
	event.SetSource(source)
	cs.onDidChangeConfigurationEmitter.Fire(event)
}

// ConfigurationEditing handles JSON file editing with queuing
type ConfigurationEditing struct {
	settingsResource     *basecommon.URI
	fileService          filescommon.IFileService
	configurationService IConfigurationService
	queue                *basecommon.Queue[interface{}]
	formattingOptions    *basecommon.FormattingOptions
}

// NewConfigurationEditing creates a new configuration editing instance
func NewConfigurationEditing(
	settingsResource *basecommon.URI,
	fileService filescommon.IFileService,
	configurationService IConfigurationService,
) *ConfigurationEditing {
	return &ConfigurationEditing{
		settingsResource:     settingsResource,
		fileService:          fileService,
		configurationService: configurationService,
		queue:                basecommon.NewQueue[interface{}](),
	}
}

// Write writes a configuration value to the settings file
func (ce *ConfigurationEditing) Write(path basecommon.JSONPath, value interface{}) error {
	resultChan := ce.queue.Queue(func() <-chan interface{} {
		result := make(chan interface{}, 1)
		go func() {
			defer close(result)
			err := ce.doWriteConfiguration(path, value)
			result <- err
		}()
		return result
	})

	// Wait for the result
	result := <-resultChan
	if err, ok := result.(error); ok {
		return err
	}
	return nil
}

// doWriteConfiguration performs the actual configuration writing
func (ce *ConfigurationEditing) doWriteConfiguration(path basecommon.JSONPath, value interface{}) error {
	var content string

	// Try to read existing file
	fileContent, err := ce.fileService.ReadFile(ce.settingsResource, nil, nil)
	if err != nil {
		if filescommon.ToFileOperationResult(err) == filescommon.FileOperationResultFileNotFound {
			content = "{}"
		} else {
			return err
		}
	} else {
		content = fileContent.Value.ToString()
	}

	// Parse JSON to check for errors
	errors := []basecommon.ParseError{}
	basecommon.Parse(content, &errors, &basecommon.ParseOptions{
		AllowTrailingComma: true,
		AllowEmptyContent:  true,
	})

	if len(errors) > 0 {
		return fmt.Errorf("unable to write into the settings file. Please open the file to correct errors/warnings in the file and try again")
	}

	// Get edits
	edits := ce.getEdits(content, path, value)
	content = basecommon.ApplyEdits(content, edits)

	// Write file
	buffer := basecommon.VSBufferFromString(content)
	_, err = ce.fileService.WriteFile(ce.settingsResource, buffer, nil)
	return err
}

// getEdits generates edits for the configuration change
func (ce *ConfigurationEditing) getEdits(content string, path basecommon.JSONPath, value interface{}) []basecommon.Edit {
	options := ce.getFormattingOptions()

	// With empty path the entire file is being replaced
	if len(path) == 0 {
		var contentStr string
		if value != nil {
			if options.InsertSpaces != nil && *options.InsertSpaces {
				indent := ""
				if options.TabSize != nil {
					for i := 0; i < *options.TabSize; i++ {
						indent += " "
					}
				}
				// Simple JSON formatting
				contentStr = fmt.Sprintf("%v", value)
			} else {
				contentStr = fmt.Sprintf("%v", value)
			}
		} else {
			contentStr = ""
		}

		return []basecommon.Edit{{
			Offset:  0,
			Length:  len(content),
			Content: contentStr,
		}}
	}

	return basecommon.SetProperty(content, path, value, *options, nil)
}

// getFormattingOptions returns formatting options for JSON editing
func (ce *ConfigurationEditing) getFormattingOptions() *basecommon.FormattingOptions {
	if ce.formattingOptions != nil {
		return ce.formattingOptions
	}

	// Get EOL setting
	eol := "\n"
	if basecommon.IsWindows {
		eol = "\r\n"
	}

	// For now, use default values since we need to fix the GetValue signature first
	// TODO: Fix GetValue to support overrides properly

	// Get insert spaces setting
	insertSpaces := true

	// Get tab size setting
	tabSize := 4

	ce.formattingOptions = &basecommon.FormattingOptions{
		Eol:          &eol,
		InsertSpaces: &insertSpaces,
		TabSize:      &tabSize,
	}

	return ce.formattingOptions
}
