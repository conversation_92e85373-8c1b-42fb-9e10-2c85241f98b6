/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"regexp"
	"strings"
	"sync"

	basecommon "src/vs/base/common"
	registrycommon "src/vs/platform/registry/common"
)

// EditPresentationTypes represents edit presentation types
type EditPresentationTypes string

const (
	EditPresentationTypesMultiline  EditPresentationTypes = "multilineText"
	EditPresentationTypesSingleline EditPresentationTypes = "singlelineText"
)

// Extensions contains configuration extension identifiers
var Extensions = struct {
	Configuration string
}{
	Configuration: "base.contributions.configuration",
}

// ConfigurationScope represents the scope of a configuration
type ConfigurationScope int

const (
	ConfigurationScopeApplication ConfigurationScope = iota + 1
	ConfigurationScopeWindow
	ConfigurationScopeResource
	ConfigurationScopeLanguageOverridable
	ConfigurationScopeMachine
	ConfigurationScopeMachineOverridable
)

// IExtensionInfo represents extension information
type IExtensionInfo struct {
	ID          string  `json:"id"`
	DisplayName *string `json:"displayName,omitempty"`
}

// IConfigurationPropertySchema represents a configuration property schema
type IConfigurationPropertySchema struct {
	Type                 interface{}            `json:"type,omitempty"`
	Title                *string                `json:"title,omitempty"`
	Description          *string                `json:"description,omitempty"`
	Default              interface{}            `json:"default,omitempty"`
	Scope                *ConfigurationScope    `json:"scope,omitempty"`
	Enum                 []interface{}          `json:"enum,omitempty"`
	EnumDescriptions     []string               `json:"enumDescriptions,omitempty"`
	EnumItemLabels       []string               `json:"enumItemLabels,omitempty"`
	Minimum              *float64               `json:"minimum,omitempty"`
	Maximum              *float64               `json:"maximum,omitempty"`
	Pattern              *string                `json:"pattern,omitempty"`
	PatternErrorMessage  *string                `json:"patternErrorMessage,omitempty"`
	UniqueItems          *bool                  `json:"uniqueItems,omitempty"`
	Items                interface{}            `json:"items,omitempty"`
	MinItems             *int                   `json:"minItems,omitempty"`
	MaxItems             *int                   `json:"maxItems,omitempty"`
	Properties           map[string]interface{} `json:"properties,omitempty"`
	AdditionalProperties interface{}            `json:"additionalProperties,omitempty"`
	Deprecated           *bool                  `json:"deprecated,omitempty"`
	DeprecationMessage   *string                `json:"deprecationMessage,omitempty"`
	MarkdownDescription  *string                `json:"markdownDescription,omitempty"`
	Tags                 []string               `json:"tags,omitempty"`
	Restricted           *bool                  `json:"restricted,omitempty"`
	IncludedKeys         []string               `json:"includedKeys,omitempty"`
	ExcludedKeys         []string               `json:"excludedKeys,omitempty"`
	DisallowSyncIgnore   *bool                  `json:"disallowSyncIgnore,omitempty"`
	Ignoresync           *bool                  `json:"ignoreSync,omitempty"`
	Policy               interface{}            `json:"policy,omitempty"`
	EditPresentation     *EditPresentationTypes `json:"editPresentation,omitempty"`
	Order                *int                   `json:"order,omitempty"`
}

// IConfigurationNode represents a configuration node
type IConfigurationNode struct {
	ID                   *string                                    `json:"id,omitempty"`
	Order                *int                                       `json:"order,omitempty"`
	Type                 interface{}                                `json:"type,omitempty"`
	Title                *string                                    `json:"title,omitempty"`
	Description          *string                                    `json:"description,omitempty"`
	Properties           map[string]*IConfigurationPropertySchema  `json:"properties,omitempty"`
	AllOf                []*IConfigurationNode                      `json:"allOf,omitempty"`
	Scope                *ConfigurationScope                        `json:"scope,omitempty"`
	Extensioninfo        *IExtensionInfo                            `json:"extensionInfo,omitempty"`
	RestrictedProperties []string                                   `json:"restrictedProperties,omitempty"`
}

// IConfigurationDefaults represents configuration defaults
type IConfigurationDefaults struct {
	Overrides map[string]interface{} `json:"overrides"`
	Source    *IExtensionInfo         `json:"source,omitempty"`
}

// IConfigurationDelta represents changes to configuration
type IConfigurationDelta struct {
	RemovedDefaults      []*IConfigurationDefaults `json:"removedDefaults,omitempty"`
	RemovedConfigurations []*IConfigurationNode     `json:"removedConfigurations,omitempty"`
	AddedDefaults        []*IConfigurationDefaults `json:"addedDefaults,omitempty"`
	AddedConfigurations  []*IConfigurationNode     `json:"addedConfigurations,omitempty"`
}

// IConfigurationDefaultOverrideValue represents a default override value
type IConfigurationDefaultOverrideValue struct {
	Value  interface{}     `json:"value"`
	Source *IExtensionInfo `json:"source,omitempty"`
}

// IConfigurationRegistry represents the configuration registry interface
type IConfigurationRegistry interface {
	// RegisterConfiguration registers a configuration to the registry
	RegisterConfiguration(configuration *IConfigurationNode) *IConfigurationNode
	
	// RegisterConfigurations registers multiple configurations to the registry
	RegisterConfigurations(configurations []*IConfigurationNode, validate bool)
	
	// DeregisterConfigurations deregisters multiple configurations from the registry
	DeregisterConfigurations(configurations []*IConfigurationNode)
	
	// UpdateConfigurations updates the configuration registry
	UpdateConfigurations(configurations struct {
		Add    []*IConfigurationNode
		Remove []*IConfigurationNode
	})
	
	// RegisterDefaultConfigurations registers multiple default configurations
	RegisterDefaultConfigurations(defaultConfigurations []*IConfigurationDefaults)
	
	// DeregisterDefaultConfigurations deregisters multiple default configurations
	DeregisterDefaultConfigurations(defaultConfigurations []*IConfigurationDefaults)
	
	// DeltaConfiguration bulk update of the configuration registry
	DeltaConfiguration(delta *IConfigurationDelta)
	
	// GetRegisteredDefaultConfigurations returns the registered default configurations
	GetRegisteredDefaultConfigurations() []*IConfigurationDefaults
	
	// GetConfigurationDefaultsOverrides returns the registered configuration defaults overrides
	GetConfigurationDefaultsOverrides() map[string]*IConfigurationDefaultOverrideValue
	
	// NotifyConfigurationSchemaUpdated signals that the schema of a configuration setting has changed
	NotifyConfigurationSchemaUpdated(configurations ...*IConfigurationNode)
	
	// OnDidSchemaChange event that fires whenever a configuration has been registered
	OnDidSchemaChange() basecommon.Event[interface{}]
	
	// OnDidUpdateConfiguration event that fires whenever a configuration has been updated
	OnDidUpdateConfiguration() basecommon.Event[struct {
		Properties       map[string]bool `json:"properties"`
		DefaultsOverrides *bool          `json:"defaultsOverrides,omitempty"`
	}]
	
	// GetConfigurationProperties returns all configuration properties
	GetConfigurationProperties() map[string]*IConfigurationPropertySchema
}

// ConfigurationRegistry implements IConfigurationRegistry
type ConfigurationRegistry struct {
	mu                              sync.RWMutex
	configurationProperties         map[string]*IConfigurationPropertySchema
	configurationDefaultsOverrides map[string]*IConfigurationDefaultOverrideValue
	defaultConfigurations           []*IConfigurationDefaults
	onDidSchemaChangeEmitter        *basecommon.Emitter[interface{}]
	onDidUpdateConfigurationEmitter *basecommon.Emitter[struct {
		Properties        map[string]bool `json:"properties"`
		DefaultsOverrides *bool           `json:"defaultsOverrides,omitempty"`
	}]
}

// NewConfigurationRegistry creates a new configuration registry
func NewConfigurationRegistry() IConfigurationRegistry {
	return &ConfigurationRegistry{
		configurationProperties:         make(map[string]*IConfigurationPropertySchema),
		configurationDefaultsOverrides: make(map[string]*IConfigurationDefaultOverrideValue),
		defaultConfigurations:           []*IConfigurationDefaults{},
		onDidSchemaChangeEmitter:        basecommon.NewEmitter[interface{}](),
		onDidUpdateConfigurationEmitter: basecommon.NewEmitter[struct {
			Properties        map[string]bool `json:"properties"`
			DefaultsOverrides *bool           `json:"defaultsOverrides,omitempty"`
		}](),
	}
}

func (r *ConfigurationRegistry) RegisterConfiguration(configuration *IConfigurationNode) *IConfigurationNode {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if configuration.Properties != nil {
		for key, property := range configuration.Properties {
			r.configurationProperties[key] = property
		}
	}
	
	r.onDidSchemaChangeEmitter.Fire(nil)
	return configuration
}

func (r *ConfigurationRegistry) RegisterConfigurations(configurations []*IConfigurationNode, validate bool) {
	for _, config := range configurations {
		r.RegisterConfiguration(config)
	}
}

func (r *ConfigurationRegistry) DeregisterConfigurations(configurations []*IConfigurationNode) {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	for _, config := range configurations {
		if config.Properties != nil {
			for key := range config.Properties {
				delete(r.configurationProperties, key)
			}
		}
	}
	
	r.onDidSchemaChangeEmitter.Fire(nil)
}

func (r *ConfigurationRegistry) UpdateConfigurations(configurations struct {
	Add    []*IConfigurationNode
	Remove []*IConfigurationNode
}) {
	r.DeregisterConfigurations(configurations.Remove)
	r.RegisterConfigurations(configurations.Add, false)
}

func (r *ConfigurationRegistry) RegisterDefaultConfigurations(defaultConfigurations []*IConfigurationDefaults) {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	r.defaultConfigurations = append(r.defaultConfigurations, defaultConfigurations...)
	
	for _, defaults := range defaultConfigurations {
		for key, value := range defaults.Overrides {
			r.configurationDefaultsOverrides[key] = &IConfigurationDefaultOverrideValue{
				Value:  value,
				Source: defaults.Source,
			}
		}
	}
}

func (r *ConfigurationRegistry) DeregisterDefaultConfigurations(defaultConfigurations []*IConfigurationDefaults) {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	// Remove from defaultConfigurations slice
	for _, toRemove := range defaultConfigurations {
		for i, existing := range r.defaultConfigurations {
			if existing == toRemove {
				r.defaultConfigurations = append(r.defaultConfigurations[:i], r.defaultConfigurations[i+1:]...)
				break
			}
		}
		
		// Remove from overrides
		for key := range toRemove.Overrides {
			delete(r.configurationDefaultsOverrides, key)
		}
	}
}

func (r *ConfigurationRegistry) DeltaConfiguration(delta *IConfigurationDelta) {
	if delta.RemovedDefaults != nil {
		r.DeregisterDefaultConfigurations(delta.RemovedDefaults)
	}
	if delta.RemovedConfigurations != nil {
		r.DeregisterConfigurations(delta.RemovedConfigurations)
	}
	if delta.AddedDefaults != nil {
		r.RegisterDefaultConfigurations(delta.AddedDefaults)
	}
	if delta.AddedConfigurations != nil {
		r.RegisterConfigurations(delta.AddedConfigurations, false)
	}
}

func (r *ConfigurationRegistry) GetRegisteredDefaultConfigurations() []*IConfigurationDefaults {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	result := make([]*IConfigurationDefaults, len(r.defaultConfigurations))
	copy(result, r.defaultConfigurations)
	return result
}

func (r *ConfigurationRegistry) GetConfigurationDefaultsOverrides() map[string]*IConfigurationDefaultOverrideValue {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	result := make(map[string]*IConfigurationDefaultOverrideValue)
	for k, v := range r.configurationDefaultsOverrides {
		result[k] = v
	}
	return result
}

func (r *ConfigurationRegistry) NotifyConfigurationSchemaUpdated(configurations ...*IConfigurationNode) {
	r.onDidSchemaChangeEmitter.Fire(nil)
}

func (r *ConfigurationRegistry) OnDidSchemaChange() basecommon.Event[interface{}] {
	return r.onDidSchemaChangeEmitter.Event()
}

func (r *ConfigurationRegistry) OnDidUpdateConfiguration() basecommon.Event[struct {
	Properties        map[string]bool `json:"properties"`
	DefaultsOverrides *bool           `json:"defaultsOverrides,omitempty"`
}] {
	return r.onDidUpdateConfigurationEmitter.Event()
}

func (r *ConfigurationRegistry) GetConfigurationProperties() map[string]*IConfigurationPropertySchema {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	result := make(map[string]*IConfigurationPropertySchema)
	for k, v := range r.configurationProperties {
		result[k] = v
	}
	return result
}

// Global registry instance
var configurationRegistry IConfigurationRegistry

func init() {
	configurationRegistry = NewConfigurationRegistry()
	registrycommon.Add(Extensions.Configuration, configurationRegistry)
}

// GetConfigurationRegistry returns the global configuration registry
func GetConfigurationRegistry() IConfigurationRegistry {
	return configurationRegistry
}

// Override identifier patterns and functions
var (
	OverrideIdentifierPattern = `\[([^\]]+)\]`
	OverrideIdentifierRegex   = regexp.MustCompile(OverrideIdentifierPattern)
	OverridePropertyPattern   = `^(` + OverrideIdentifierPattern + `)+$`
	OverridePropertyRegex     = regexp.MustCompile(OverridePropertyPattern)
)

// OverrideIdentifiersFromKey extracts override identifiers from a key
func OverrideIdentifiersFromKey(key string) []string {
	identifiers := []string{}
	if OverridePropertyRegex.MatchString(key) {
		matches := OverrideIdentifierRegex.FindAllStringSubmatch(key, -1)
		for _, match := range matches {
			if len(match) > 1 {
				identifier := strings.TrimSpace(match[1])
				if identifier != "" {
					identifiers = append(identifiers, identifier)
				}
			}
		}
	}
	return basecommon.Distinct(identifiers)
}

// KeyFromOverrideIdentifiers creates a key from override identifiers
func KeyFromOverrideIdentifiers(overrideIdentifiers []string) string {
	result := ""
	for _, identifier := range overrideIdentifiers {
		result += "[" + identifier + "]"
	}
	return result
}
